import { createMocks } from 'node-mocks-http'
import { GET, POST } from '@/app/api/appointments/route'

// Mock the auth and database utilities
jest.mock('@/lib/api-utils', () => ({
  withAuth: jest.fn((request, handler) => handler('user-123', mockSupabase)),
  withAuthAndPermission: jest.fn((request, resource, action, handler) => handler('user-123', mockSupabase)),
  createApiResponse: jest.fn((data, message, status = 200) => 
    new Response(JSON.stringify({ data, message }), { status })
  ),
  handleApiError: jest.fn((error) => 
    new Response(JSON.stringify({ error: error.message }), { status: 500 })
  ),
}))

const mockSupabase = {
  from: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    single: jest.fn(),
  })),
}

describe('/api/appointments', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET', () => {
    it('should return appointments for authenticated user', async () => {
      const mockAppointments = [
        {
          id: '1',
          title: 'Consulta - João Silva',
          patient_id: 'patient-1',
          start_time: '2024-01-15T09:00:00Z',
          end_time: '2024-01-15T10:00:00Z',
          status: 'scheduled',
          patients: { name: 'João Silva' },
          healthcare_professionals: { name: 'Dr. Maria', specialty: 'Cardiologia' }
        }
      ]

      mockSupabase.from().single.mockResolvedValue({ data: mockAppointments, error: null })

      const { req } = createMocks({
        method: 'GET',
        url: '/api/appointments',
      })

      const response = await GET(req as any)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.data).toEqual(mockAppointments)
      expect(mockSupabase.from).toHaveBeenCalledWith('appointments')
    })

    it('should filter appointments by date when date parameter is provided', async () => {
      const { req } = createMocks({
        method: 'GET',
        url: '/api/appointments?date=2024-01-15',
      })

      mockSupabase.from().single.mockResolvedValue({ data: [], error: null })

      await GET(req as any)

      expect(mockSupabase.from().gte).toHaveBeenCalled()
      expect(mockSupabase.from().lt).toHaveBeenCalled()
    })

    it('should filter appointments by patient when patient_id parameter is provided', async () => {
      const { req } = createMocks({
        method: 'GET',
        url: '/api/appointments?patient_id=patient-123',
      })

      mockSupabase.from().single.mockResolvedValue({ data: [], error: null })

      await GET(req as any)

      expect(mockSupabase.from().eq).toHaveBeenCalledWith('patient_id', 'patient-123')
    })

    it('should handle database errors', async () => {
      mockSupabase.from().single.mockResolvedValue({ 
        data: null, 
        error: { message: 'Database error' } 
      })

      const { req } = createMocks({
        method: 'GET',
        url: '/api/appointments',
      })

      const response = await GET(req as any)

      expect(response.status).toBe(500)
    })
  })

  describe('POST', () => {
    it('should create a new appointment', async () => {
      const newAppointment = {
        title: 'Nova Consulta',
        patient_id: 'patient-1',
        start_time: '2024-01-15T09:00:00Z',
        end_time: '2024-01-15T10:00:00Z',
        type: 'consultation',
        status: 'scheduled'
      }

      const createdAppointment = {
        id: 'appointment-1',
        ...newAppointment,
        user_id: 'user-123',
        patients: { name: 'João Silva' }
      }

      mockSupabase.from().single.mockResolvedValue({ 
        data: createdAppointment, 
        error: null 
      })

      const { req } = createMocks({
        method: 'POST',
        body: newAppointment,
      })

      const response = await POST(req as any)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.data).toEqual(createdAppointment)
      expect(mockSupabase.from).toHaveBeenCalledWith('appointments')
      expect(mockSupabase.from().insert).toHaveBeenCalled()
    })

    it('should create appointment with procedures', async () => {
      const appointmentWithProcedures = {
        title: 'Consulta com Procedimentos',
        patient_id: 'patient-1',
        start_time: '2024-01-15T09:00:00Z',
        end_time: '2024-01-15T10:00:00Z',
        type: 'consultation',
        procedures: [
          {
            procedure_id: 'proc-1',
            quantity: 1,
            unit_price: 100,
            total_price: 100
          }
        ],
        total_price: 100
      }

      const createdAppointment = {
        id: 'appointment-1',
        title: 'Consulta com Procedimentos',
        patient_id: 'patient-1',
        user_id: 'user-123'
      }

      // Mock successful appointment creation
      mockSupabase.from().single.mockResolvedValueOnce({ 
        data: createdAppointment, 
        error: null 
      })

      // Mock successful procedure creation
      mockSupabase.from().insert.mockResolvedValueOnce({ 
        data: [], 
        error: null 
      })

      const { req } = createMocks({
        method: 'POST',
        body: appointmentWithProcedures,
      })

      const response = await POST(req as any)

      expect(response.status).toBe(201)
      expect(mockSupabase.from).toHaveBeenCalledWith('appointments')
      expect(mockSupabase.from).toHaveBeenCalledWith('appointment_procedures')
    })

    it('should handle validation errors', async () => {
      const invalidAppointment = {
        // Missing required fields
        title: '',
        patient_id: 'invalid-uuid'
      }

      const { req } = createMocks({
        method: 'POST',
        body: invalidAppointment,
      })

      // This would be caught by validation middleware in real implementation
      mockSupabase.from().single.mockResolvedValue({ 
        data: null, 
        error: { message: 'Validation error' } 
      })

      const response = await POST(req as any)

      expect(response.status).toBe(500)
    })

    it('should rollback appointment if procedure creation fails', async () => {
      const appointmentWithProcedures = {
        title: 'Consulta com Procedimentos',
        patient_id: 'patient-1',
        start_time: '2024-01-15T09:00:00Z',
        end_time: '2024-01-15T10:00:00Z',
        type: 'consultation',
        procedures: [
          {
            procedure_id: 'proc-1',
            quantity: 1,
            unit_price: 100,
            total_price: 100
          }
        ]
      }

      const createdAppointment = {
        id: 'appointment-1',
        title: 'Consulta com Procedimentos'
      }

      // Mock successful appointment creation
      mockSupabase.from().single.mockResolvedValueOnce({ 
        data: createdAppointment, 
        error: null 
      })

      // Mock failed procedure creation
      mockSupabase.from().insert.mockResolvedValueOnce({ 
        data: null, 
        error: { message: 'Procedure creation failed' } 
      })

      // Mock appointment deletion (rollback)
      mockSupabase.from().delete.mockResolvedValueOnce({ 
        data: null, 
        error: null 
      })

      const { req } = createMocks({
        method: 'POST',
        body: appointmentWithProcedures,
      })

      const response = await POST(req as any)

      expect(response.status).toBe(500)
      expect(mockSupabase.from().delete).toHaveBeenCalled()
    })
  })
})
