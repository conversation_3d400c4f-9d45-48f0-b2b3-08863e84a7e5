"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/AppSidebar.tsx":
/*!***************************************!*\
  !*** ./src/components/AppSidebar.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,LayoutDashboard,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,LayoutDashboard,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,LayoutDashboard,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,LayoutDashboard,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,LayoutDashboard,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,LayoutDashboard,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst sidebarItems = [\n    {\n        title: \"Dashboard\",\n        url: \"/dashboard\",\n        icon: _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        title: \"Agenda\",\n        url: \"/dashboard/agenda\",\n        icon: _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        title: \"Pacientes\",\n        url: \"/dashboard/pacientes\",\n        icon: _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        title: \"Configurações\",\n        url: \"/dashboard/configuracoes\",\n        icon: _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    }\n];\nconst adminItems = [\n    {\n        title: \"Usuários\",\n        url: \"/dashboard/admin/usuarios\",\n        icon: _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    }\n];\nfunction AppSidebar() {\n    _s();\n    const { state } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isAdmin, loading, userRoles } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    // Debug log\n    console.log('🔍 Sidebar permissions:', {\n        isAdmin: isAdmin ? isAdmin() : false,\n        loading,\n        userRoles\n    });\n    const isActive = (path)=>pathname === path;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n        collapsible: \"icon\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarContent, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-8 w-8 text-primary mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            state === \"expanded\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-foreground\",\n                                children: \"Nectar Sa\\xfade\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 38\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroup, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroupLabel, {\n                            children: \"Menu Principal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroupContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenu, {\n                                children: sidebarItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.url,\n                                                className: isActive(item.url) ? \"bg-primary/10 text-primary font-medium border-r-2 border-primary\" : \"hover:bg-muted/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"mr-3 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    state === \"expanded\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 48\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, item.title, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                isAdmin() && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroup, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroupLabel, {\n                            children: \"Administra\\xe7\\xe3o\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroupContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenu, {\n                                children: adminItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.url,\n                                                className: isActive(item.url) ? \"bg-primary/10 text-primary font-medium border-r-2 border-primary\" : \"hover:bg-muted/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"mr-3 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    state === \"expanded\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 50\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, item.title, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(AppSidebar, \"pRNY4f/X0d6BnP8jgUuXluiQpPc=\", false, function() {\n    return [\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AppSidebar.tsx\n"));

/***/ })

});