import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Check if user is admin
      const { data: userRoles, error: rolesError } = await supabase
        .from('user_roles')
        .select('role_name')
        .eq('user_id', userId)

      if (rolesError) {
        return handleApiError(rolesError)
      }

      const isAdmin = userRoles?.some(role => role.role_name === 'admin')
      if (!isAdmin) {
        return createApiResponse(null, 'Forbidden', 403)
      }

      // Get total users count
      const { count: totalUsers } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })

      // Get active users count
      const { count: activeUsers } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)

      // Get inactive users count
      const { count: inactiveUsers } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', false)

      // Get unique roles count
      const { data: roles } = await supabase
        .from('user_roles')
        .select('role_name')

      const uniqueRoles = new Set(roles?.map(r => r.role_name) || [])

      const stats = {
        totalUsers: totalUsers || 0,
        activeUsers: activeUsers || 0,
        inactiveUsers: inactiveUsers || 0,
        totalRoles: uniqueRoles.size
      }

      return createApiResponse(stats)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
