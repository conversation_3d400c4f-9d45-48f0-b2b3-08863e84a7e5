"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/usuarios/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/admin/usuarios/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/admin/usuarios/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UsersAdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction UsersAdminPage() {\n    _s();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDialogOpen, setIsDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('users');\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    // Form states\n    const [userForm, setUserForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        name: '',\n        phone: '',\n        password: '',\n        role: 'secretary',\n        is_active: true\n    });\n    const [roleForm, setRoleForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        user_id: '',\n        role_name: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersAdminPage.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"UsersAdminPage.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            setLoading(true);\n            await Promise.all([\n                fetchUsers(),\n                fetchUserRoles(),\n                fetchPermissions()\n            ]);\n        } catch (error) {\n            console.error('Error fetching data:', error);\n            toast({\n                title: \"Erro ao carregar dados\",\n                description: \"Não foi possível carregar os dados dos usuários.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchUsers = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_12__.makeAuthenticatedRequest)('/api/admin/users');\n            if (response.ok) {\n                const result = await response.json();\n                setUsers(result.data || []);\n            }\n        } catch (error) {\n            console.error('Error fetching users:', error);\n        }\n    };\n    const fetchUserRoles = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_12__.makeAuthenticatedRequest)('/api/user-roles');\n            if (response.ok) {\n                const result = await response.json();\n                setUserRoles(result.data || []);\n            }\n        } catch (error) {\n            console.error('Error fetching user roles:', error);\n        }\n    };\n    const fetchPermissions = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_12__.makeAuthenticatedRequest)('/api/permissions');\n            if (response.ok) {\n                const result = await response.json();\n                setPermissions(result.data || []);\n            }\n        } catch (error) {\n            console.error('Error fetching permissions:', error);\n        }\n    };\n    const handleCreateUser = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_12__.makeAuthenticatedRequest)('/api/admin/users', {\n                method: 'POST',\n                body: JSON.stringify(userForm)\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Usuário criado\",\n                    description: \"Usuário criado com sucesso.\"\n                });\n                setIsDialogOpen(false);\n                setUserForm({\n                    email: '',\n                    name: '',\n                    phone: '',\n                    role: 'assistant',\n                    is_active: true\n                });\n                fetchUsers();\n            } else {\n                throw new Error('Failed to create user');\n            }\n        } catch (error) {\n            console.error('Error creating user:', error);\n            toast({\n                title: \"Erro ao criar usuário\",\n                description: \"Não foi possível criar o usuário.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAssignRole = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_12__.makeAuthenticatedRequest)('/api/user-roles', {\n                method: 'POST',\n                body: JSON.stringify(roleForm)\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Role atribuído\",\n                    description: \"Role atribuído com sucesso ao usuário.\"\n                });\n                setRoleForm({\n                    user_id: '',\n                    role_name: ''\n                });\n                fetchUserRoles();\n            } else {\n                throw new Error('Failed to assign role');\n            }\n        } catch (error) {\n            console.error('Error assigning role:', error);\n            toast({\n                title: \"Erro ao atribuir role\",\n                description: \"Não foi possível atribuir o role ao usuário.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleToggleUserStatus = async (userId, isActive)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_12__.makeAuthenticatedRequest)(\"/api/admin/users/\".concat(userId), {\n                method: 'PATCH',\n                body: JSON.stringify({\n                    is_active: !isActive\n                })\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Status atualizado\",\n                    description: \"Usu\\xe1rio \".concat(!isActive ? 'ativado' : 'desativado', \" com sucesso.\")\n                });\n                fetchUsers();\n            } else {\n                throw new Error('Failed to update user status');\n            }\n        } catch (error) {\n            console.error('Error updating user status:', error);\n            toast({\n                title: \"Erro ao atualizar status\",\n                description: \"Não foi possível atualizar o status do usuário.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const getUserRoles = (userId)=>{\n        return userRoles.filter((ur)=>ur.user_id === userId).map((ur)=>ur.role_name);\n    };\n    const getRolePermissions = (roleName)=>{\n        return permissions.filter((p)=>p.role_name === roleName);\n    };\n    const availableRoles = [\n        'admin',\n        'doctor',\n        'secretary',\n        'assistant'\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-muted-foreground\",\n                            children: \"Carregando...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"Administra\\xe7\\xe3o de Usu\\xe1rios\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Gerencie usu\\xe1rios, roles e permiss\\xf5es do sistema\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                        open: isDialogOpen,\n                        onOpenChange: setIsDialogOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Novo Usu\\xe1rio\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                                children: \"Criar Novo Usu\\xe1rio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                                children: \"Adicione um novo usu\\xe1rio ao sistema\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"email\",\n                                                        children: \"E-mail *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"email\",\n                                                        type: \"email\",\n                                                        value: userForm.email,\n                                                        onChange: (e)=>setUserForm({\n                                                                ...userForm,\n                                                                email: e.target.value\n                                                            }),\n                                                        placeholder: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"name\",\n                                                        children: \"Nome\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"name\",\n                                                        value: userForm.name,\n                                                        onChange: (e)=>setUserForm({\n                                                                ...userForm,\n                                                                name: e.target.value\n                                                            }),\n                                                        placeholder: \"Nome completo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"phone\",\n                                                        children: \"Telefone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"phone\",\n                                                        value: userForm.phone,\n                                                        onChange: (e)=>setUserForm({\n                                                                ...userForm,\n                                                                phone: e.target.value\n                                                            }),\n                                                        placeholder: \"(11) 99999-9999\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"role\",\n                                                        children: \"Role Inicial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        value: userForm.role,\n                                                        onValueChange: (value)=>setUserForm({\n                                                                ...userForm,\n                                                                role: value\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: availableRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: role,\n                                                                        children: role.charAt(0).toUpperCase() + role.slice(1)\n                                                                    }, role, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogFooter, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>setIsDialogOpen(false),\n                                                children: \"Cancelar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleCreateUser,\n                                                children: \"Criar Usu\\xe1rio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.Tabs, {\n                value: activeTab,\n                onValueChange: setActiveTab,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsList, {\n                        className: \"grid w-full grid-cols-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"users\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Usu\\xe1rios\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"roles\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Roles\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"permissions\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Permiss\\xf5es\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"users\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            children: \"Usu\\xe1rios do Sistema\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Lista de todos os usu\\xe1rios cadastrados no sistema\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                            children: \"Nome\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                            children: \"E-mail\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                            children: \"Telefone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                            children: \"Roles\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                            children: \"A\\xe7\\xf5es\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                                children: users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                className: \"font-medium\",\n                                                                children: user.name || 'Sem nome'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                children: user.phone || '-'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: getUserRoles(user.id).map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: role\n                                                                        }, role, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                            lineNumber: 365,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: user.is_active ? \"default\" : \"destructive\",\n                                                                    children: user.is_active ? 'Ativo' : 'Inativo'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>setSelectedUser(user),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                                lineNumber: 383,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                            lineNumber: 378,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>handleToggleUserStatus(user.id, user.is_active),\n                                                                            children: user.is_active ? 'Desativar' : 'Ativar'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, user.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"roles\",\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Atribui\\xe7\\xe3o de Roles\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Atribua roles aos usu\\xe1rios do sistema\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"user_select\",\n                                                                children: \"Usu\\xe1rio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                value: roleForm.user_id,\n                                                                onValueChange: (value)=>setRoleForm({\n                                                                        ...roleForm,\n                                                                        user_id: value\n                                                                    }),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Selecione um usu\\xe1rio\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: user.id,\n                                                                                children: user.name || user.email\n                                                                            }, user.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                                lineNumber: 420,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"role_select\",\n                                                                children: \"Role\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                value: roleForm.role_name,\n                                                                onValueChange: (value)=>setRoleForm({\n                                                                        ...roleForm,\n                                                                        role_name: value\n                                                                    }),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Selecione um role\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                            lineNumber: 431,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                        lineNumber: 430,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: availableRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: role,\n                                                                                children: role.charAt(0).toUpperCase() + role.slice(1)\n                                                                            }, role, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                                lineNumber: 435,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleAssignRole,\n                                                disabled: !roleForm.user_id || !roleForm.role_name,\n                                                children: \"Atribuir Role\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Roles Atribu\\xeddos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Lista de todos os roles atribu\\xeddos aos usu\\xe1rios\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                                children: \"Usu\\xe1rio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                                children: \"E-mail\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                                children: \"Role\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                                children: \"Data de Atribui\\xe7\\xe3o\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                                    children: userRoles.map((userRole)=>{\n                                                        const user = users.find((u)=>u.id === userRole.user_id);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                    children: (user === null || user === void 0 ? void 0 : user.name) || 'Usuário não encontrado'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                    children: (user === null || user === void 0 ? void 0 : user.email) || '-'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        children: userRole.role_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                    children: new Date(userRole.created_at).toLocaleDateString('pt-BR')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, userRole.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"permissions\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            children: \"Matriz de Permiss\\xf5es\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Visualize as permiss\\xf5es de cada role no sistema\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: availableRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold capitalize\",\n                                                        children: role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2\",\n                                                        children: getRolePermissions(role).map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                variant: \"outline\",\n                                                                children: [\n                                                                    permission.resource,\n                                                                    \":\",\n                                                                    permission.action\n                                                                ]\n                                                            }, \"\".concat(permission.resource, \"-\").concat(permission.action), true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    getRolePermissions(role).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"Nenhuma permiss\\xe3o atribu\\xedda\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, role, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                lineNumber: 318,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\n_s(UsersAdminPage, \"1NZCZHe5qk8YbkaAliY57TO8gyk=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast\n    ];\n});\n_c = UsersAdminPage;\nvar _c;\n$RefreshReg$(_c, \"UsersAdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/admin/usuarios/page.tsx\n"));

/***/ })

});