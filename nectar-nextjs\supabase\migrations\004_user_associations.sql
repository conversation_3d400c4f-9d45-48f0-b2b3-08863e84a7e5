-- Migration 004: User Associations System
-- Creates the multi-user access system with hierarchical permissions

-- 1. Create user_associations table
CREATE TABLE IF NOT EXISTS user_associations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  accessor_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  target_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  association_type TEXT NOT NULL CHECK (association_type IN ('secretary_to_doctor', 'doctor_to_doctor')),
  permissions JSONB, -- Specific permissions for this association (overrides role defaults)
  is_active BOOLEAN DEFAULT true,
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Prevent self-association
  CONSTRAINT no_self_association CHECK (accessor_user_id != target_user_id),
  
  -- Unique association per type
  CONSTRAINT unique_association UNIQUE (accessor_user_id, target_user_id, association_type)
);

-- 2. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_associations_accessor ON user_associations(accessor_user_id);
CREATE INDEX IF NOT EXISTS idx_user_associations_target ON user_associations(target_user_id);
CREATE INDEX IF NOT EXISTS idx_user_associations_active ON user_associations(is_active);
CREATE INDEX IF NOT EXISTS idx_user_associations_type ON user_associations(association_type);

-- 3. Enable RLS
ALTER TABLE user_associations ENABLE ROW LEVEL SECURITY;

-- 4. Create RLS policies for user_associations
CREATE POLICY "Users can view their own associations" ON user_associations
  FOR SELECT USING (
    accessor_user_id = auth.uid() OR 
    target_user_id = auth.uid() OR
    created_by = auth.uid()
  );

CREATE POLICY "Admins can manage all associations" ON user_associations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() AND role_name = 'admin'
    )
  );

CREATE POLICY "Users can create associations they are part of" ON user_associations
  FOR INSERT WITH CHECK (
    (accessor_user_id = auth.uid() OR target_user_id = auth.uid()) AND
    created_by = auth.uid()
  );

CREATE POLICY "Users can update associations they created" ON user_associations
  FOR UPDATE USING (created_by = auth.uid());

CREATE POLICY "Users can delete associations they created" ON user_associations
  FOR DELETE USING (created_by = auth.uid());

-- 5. Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_user_associations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_associations_updated_at
  BEFORE UPDATE ON user_associations
  FOR EACH ROW
  EXECUTE FUNCTION update_user_associations_updated_at();

-- 6. Create function to check user access with permission granularity
CREATE OR REPLACE FUNCTION has_access_to_user(target_user_id UUID, required_resource TEXT DEFAULT NULL, required_action TEXT DEFAULT NULL)
RETURNS BOOLEAN AS $$
DECLARE
  association_permissions JSONB;
  role_has_permission BOOLEAN := FALSE;
BEGIN
  -- Own user always has access
  IF auth.uid() = target_user_id THEN
    RETURN TRUE;
  END IF;
  
  -- Admin has access to all users
  IF EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() AND role_name = 'admin'
  ) THEN
    RETURN TRUE;
  END IF;
  
  -- Check if there's an active association
  SELECT permissions INTO association_permissions
  FROM user_associations 
  WHERE accessor_user_id = auth.uid() 
    AND target_user_id = target_user_id 
    AND is_active = true
  LIMIT 1;
  
  -- No association found
  IF association_permissions IS NULL AND NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- If no specific resource/action required, just check association exists
  IF required_resource IS NULL OR required_action IS NULL THEN
    RETURN TRUE;
  END IF;
  
  -- Check specific permissions
  -- First check association-specific permissions (overrides role defaults)
  IF association_permissions IS NOT NULL THEN
    -- Check if the specific permission is granted in association
    IF association_permissions ? required_resource THEN
      RETURN (association_permissions->required_resource ? required_action);
    END IF;
    
    -- If association has permissions but doesn't specify this resource,
    -- it means this resource is not allowed for this specific association
    RETURN FALSE;
  END IF;
  
  -- No specific permissions in association, check role-based permissions
  SELECT EXISTS (
    SELECT 1 FROM user_roles ur
    JOIN permissions p ON ur.role_name = p.role_name
    WHERE ur.user_id = auth.uid()
      AND p.resource = required_resource
      AND p.action = required_action
  ) INTO role_has_permission;
  
  RETURN role_has_permission;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Create helper function to get user associations
CREATE OR REPLACE FUNCTION get_accessible_users(current_user_id UUID DEFAULT auth.uid())
RETURNS TABLE (
  user_id UUID,
  email TEXT,
  name TEXT,
  role TEXT,
  association_type TEXT,
  permissions JSONB,
  is_own_data BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  -- Own user data
  SELECT 
    u.id,
    u.email,
    u.name,
    u.role,
    NULL::TEXT as association_type,
    NULL::JSONB as permissions,
    TRUE as is_own_data
  FROM users u
  WHERE u.id = current_user_id
  
  UNION ALL
  
  -- Associated users
  SELECT 
    u.id,
    u.email,
    u.name,
    u.role,
    ua.association_type,
    ua.permissions,
    FALSE as is_own_data
  FROM users u
  JOIN user_associations ua ON u.id = ua.target_user_id
  WHERE ua.accessor_user_id = current_user_id
    AND ua.is_active = true
  
  UNION ALL
  
  -- Admin sees all users
  SELECT 
    u.id,
    u.email,
    u.name,
    u.role,
    'admin_access'::TEXT as association_type,
    NULL::JSONB as permissions,
    FALSE as is_own_data
  FROM users u
  WHERE EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = current_user_id AND role_name = 'admin'
  )
  AND u.id != current_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Create audit log table for access tracking
CREATE TABLE IF NOT EXISTS user_access_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  accessor_user_id UUID NOT NULL REFERENCES auth.users(id),
  target_user_id UUID NOT NULL REFERENCES auth.users(id),
  resource_accessed TEXT NOT NULL,
  action_performed TEXT NOT NULL,
  ip_address INET,
  user_agent TEXT,
  success BOOLEAN DEFAULT true,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. Create indexes for audit log
CREATE INDEX IF NOT EXISTS idx_user_access_logs_accessor ON user_access_logs(accessor_user_id);
CREATE INDEX IF NOT EXISTS idx_user_access_logs_target ON user_access_logs(target_user_id);
CREATE INDEX IF NOT EXISTS idx_user_access_logs_created_at ON user_access_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_access_logs_resource ON user_access_logs(resource_accessed);

-- 10. Enable RLS on audit log
ALTER TABLE user_access_logs ENABLE ROW LEVEL SECURITY;

-- 11. Create RLS policies for audit log
CREATE POLICY "Admins can view all access logs" ON user_access_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() AND role_name = 'admin'
    )
  );

CREATE POLICY "Users can view their own access logs" ON user_access_logs
  FOR SELECT USING (accessor_user_id = auth.uid());

-- 12. Create function to log access attempts
CREATE OR REPLACE FUNCTION log_user_access(
  target_user_id UUID,
  resource_accessed TEXT,
  action_performed TEXT,
  ip_address INET DEFAULT NULL,
  user_agent TEXT DEFAULT NULL,
  success BOOLEAN DEFAULT TRUE,
  error_message TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO user_access_logs (
    accessor_user_id,
    target_user_id,
    resource_accessed,
    action_performed,
    ip_address,
    user_agent,
    success,
    error_message
  )
  VALUES (
    auth.uid(),
    target_user_id,
    resource_accessed,
    action_performed,
    ip_address,
    user_agent,
    success,
    error_message
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
