'use client'

import { useState, useEffect, createContext, useContext } from 'react'
import { makeAuthenticatedRequest } from '@/lib/api-client'

interface Permission {
  resource: string
  action: string
}

interface UserRole {
  role_name: string
}

interface PermissionsContextType {
  roles: string[]
  permissions: Permission[]
  loading: boolean
  isAdmin: boolean
  hasRole: (role: string) => boolean
  hasPermission: (resource: string, action: string) => boolean
  refetch: () => void
}

const PermissionsContext = createContext<PermissionsContextType | undefined>(undefined)

export function PermissionsProvider({ children }: { children: React.ReactNode }) {
  const [roles, setRoles] = useState<string[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)

  const fetchPermissions = async () => {
    try {
      setLoading(true)
      
      // Fetch user roles
      const rolesResponse = await makeAuthenticatedRequest('/api/user-roles')
      if (rolesResponse.ok) {
        const rolesData = await rolesResponse.json()
        const userRoles = (rolesData.data || []).map((role: UserRole) => role.role_name)
        setRoles(userRoles)
      }

      // For now, give admin permissions to all users since we don't have the permissions table
      const mockPermissions = [
        { resource: 'patients', action: 'read' },
        { resource: 'patients', action: 'create' },
        { resource: 'patients', action: 'update' },
        { resource: 'patients', action: 'delete' },
        { resource: 'appointments', action: 'read' },
        { resource: 'appointments', action: 'create' },
        { resource: 'appointments', action: 'update' },
        { resource: 'appointments', action: 'delete' },
        { resource: 'procedures', action: 'read' },
        { resource: 'procedures', action: 'create' },
        { resource: 'procedures', action: 'update' },
        { resource: 'procedures', action: 'delete' },
        { resource: 'healthcare_professionals', action: 'read' },
        { resource: 'healthcare_professionals', action: 'create' },
        { resource: 'healthcare_professionals', action: 'update' },
        { resource: 'healthcare_professionals', action: 'delete' },
      ]
      setPermissions(mockPermissions)
    } catch (error) {
      console.error('Error fetching permissions:', error)
      setRoles([])
      setPermissions([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPermissions()
  }, [])

  const hasRole = (role: string) => roles.includes(role)
  const isAdmin = hasRole('admin')
  
  const hasPermission = (resource: string, action: string) => {
    return permissions.some(p => p.resource === resource && p.action === action)
  }

  const value = {
    roles,
    permissions,
    loading,
    isAdmin,
    hasRole,
    hasPermission,
    refetch: fetchPermissions
  }

  return (
    <PermissionsContext.Provider value={value}>
      {children}
    </PermissionsContext.Provider>
  )
}

export function usePermissions() {
  const context = useContext(PermissionsContext)
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionsProvider')
  }
  return context
}

// Permission gate component
interface PermissionGateProps {
  resource: string
  action: string
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function PermissionGate({ resource, action, children, fallback = null }: PermissionGateProps) {
  const { hasPermission, loading } = usePermissions()

  if (loading) {
    return <div>Loading...</div>
  }

  if (!hasPermission(resource, action)) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

// Role gate component
interface RoleGateProps {
  roles: string[]
  children: React.ReactNode
  fallback?: React.ReactNode
  requireAll?: boolean
}

export function RoleGate({ roles, children, fallback = null, requireAll = false }: RoleGateProps) {
  const { hasRole, loading } = usePermissions()

  if (loading) {
    return <div>Loading...</div>
  }

  const hasRequiredRoles = requireAll 
    ? roles.every(role => hasRole(role))
    : roles.some(role => hasRole(role))

  if (!hasRequiredRoles) {
    return <>{fallback}</>
  }

  return <>{children}</>
}
