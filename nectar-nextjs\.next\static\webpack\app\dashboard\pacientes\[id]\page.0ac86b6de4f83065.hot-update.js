"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/pacientes/[id]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/pacientes/[id]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/pacientes/[id]/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/date-utils */ \"(app-pages-browser)/./src/lib/date-utils.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PatientDetailPage = ()=>{\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    const patientId = params.id;\n    const { canViewMedicalRecords, loading: permissionsLoading } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_14__.usePermissions)();\n    const [patient, setPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editMode, setEditMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadDialogOpen, setUploadDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [medicalRecordOpen, setMedicalRecordOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAppointment, setSelectedAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [medicalRecords, setMedicalRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newRecord, setNewRecord] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [editForm, setEditForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        phone: '',\n        birth_date: '',\n        cpf: '',\n        address: '',\n        notes: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PatientDetailPage.useEffect\": ()=>{\n            if (patientId) {\n                fetchPatientData();\n                fetchAppointments();\n                fetchAttachments();\n            }\n        }\n    }[\"PatientDetailPage.useEffect\"], [\n        patientId\n    ]);\n    const fetchPatientData = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patients/\".concat(patientId));\n            if (!response.ok) throw new Error('Failed to fetch patient');\n            const result = await response.json();\n            const data = result.data || result;\n            setPatient(data);\n            setEditForm({\n                name: data.name || '',\n                email: data.email || '',\n                phone: data.phone || '',\n                birth_date: data.birth_date || '',\n                cpf: data.cpf || '',\n                address: data.address || '',\n                notes: data.notes || ''\n            });\n        } catch (error) {\n            console.error('Error fetching patient:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao carregar dados do paciente.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchAppointments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/appointments?patient_id=\".concat(patientId));\n            if (!response.ok) throw new Error('Failed to fetch appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching appointments:', error);\n            setAppointments([]);\n        }\n    };\n    const fetchAttachments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patient-attachments?patient_id=\".concat(patientId));\n            if (!response.ok) throw new Error('Failed to fetch attachments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAttachments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching attachments:', error);\n            setAttachments([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSavePatient = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patients/\".concat(patientId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(editForm)\n            });\n            if (!response.ok) throw new Error('Failed to update patient');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Dados do paciente atualizados.\"\n            });\n            setEditMode(false);\n            fetchPatientData();\n        } catch (error) {\n            console.error('Error updating patient:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar paciente.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleFileUpload = async ()=>{\n        if (!selectedFile) return;\n        try {\n            const formData = new FormData();\n            formData.append('file', selectedFile);\n            formData.append('patient_id', patientId);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)('/api/patient-attachments', {\n                method: 'POST',\n                body: formData\n            });\n            if (!response.ok) throw new Error('Failed to upload file');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Arquivo enviado com sucesso.\"\n            });\n            setUploadDialogOpen(false);\n            setSelectedFile(null);\n            fetchAttachments();\n        } catch (error) {\n            console.error('Error uploading file:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao enviar arquivo.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteAttachment = async (attachmentId)=>{\n        if (!confirm('Tem certeza que deseja excluir este arquivo?')) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patient-attachments/\".concat(attachmentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete attachment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Arquivo excluído com sucesso.\"\n            });\n            fetchAttachments();\n        } catch (error) {\n            console.error('Error deleting attachment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao excluir arquivo.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const updateAppointmentStatus = async (appointmentId, status)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Status da consulta atualizado.\"\n            });\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error updating appointment status:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar status da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDownloadAttachment = async (attachmentId, fileName)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patient-attachments/\".concat(attachmentId));\n            if (!response.ok) throw new Error('Failed to get download URL');\n            const result = await response.json();\n            const data = result.data || result;\n            // Create a temporary link to download the file\n            const link = document.createElement('a');\n            link.href = data.download_url;\n            link.download = fileName;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        } catch (error) {\n            console.error('Error downloading file:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao baixar arquivo.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (!bytes) return 'Tamanho desconhecido';\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(1024));\n        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n    };\n    const calculateAge = (birthDate)=>{\n        if (!birthDate) return 'Idade não informada';\n        const today = new Date();\n        const birth = new Date(birthDate);\n        const age = today.getFullYear() - birth.getFullYear();\n        const monthDiff = today.getMonth() - birth.getMonth();\n        if (monthDiff < 0 || monthDiff === 0 && today.getDate() < birth.getDate()) {\n            return \"\".concat(age - 1, \" anos\");\n        }\n        return \"\".concat(age, \" anos\");\n    };\n    const handleOpenMedicalRecord = async (appointment)=>{\n        setSelectedAppointment(appointment);\n        setMedicalRecordOpen(true);\n        await fetchMedicalRecords(appointment.id);\n    };\n    const fetchMedicalRecords = async (appointmentId)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/medical-records?appointment_id=\".concat(appointmentId));\n            if (!response.ok) throw new Error('Failed to fetch medical records');\n            const result = await response.json();\n            const data = result.data || result;\n            setMedicalRecords(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching medical records:', error);\n            setMedicalRecords([]);\n        }\n    };\n    const handleAddMedicalRecord = async ()=>{\n        if (!newRecord.trim() || !selectedAppointment) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)('/api/medical-records', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    appointment_id: selectedAppointment.id,\n                    patient_id: patientId,\n                    notes: newRecord.trim()\n                })\n            });\n            if (!response.ok) throw new Error('Failed to add medical record');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Registro médico adicionado.\"\n            });\n            setNewRecord('');\n            await fetchMedicalRecords(selectedAppointment.id);\n        } catch (error) {\n            console.error('Error adding medical record:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao adicionar registro médico.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleStartTreatment = async ()=>{\n        if (!selectedAppointment) return;\n        try {\n            await updateAppointmentStatus(selectedAppointment.id, 'in_progress');\n            toast({\n                title: \"Atendimento iniciado\",\n                description: \"O status da consulta foi atualizado para 'Em Andamento'.\"\n            });\n            // Refresh appointments to update the status\n            await fetchAppointments();\n            // Update the selected appointment status\n            setSelectedAppointment({\n                ...selectedAppointment,\n                status: 'in_progress'\n            });\n        } catch (error) {\n            console.error('Error starting treatment:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-[250px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 w-[200px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 w-[80px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 w-[80px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-4 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 w-[120px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 w-[150px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 w-[100px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: Array.from({\n                                            length: 4\n                                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 w-[80px] bg-muted animate-pulse rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-10 w-full bg-muted animate-pulse rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: Array.from({\n                                            length: 4\n                                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 w-[80px] bg-muted animate-pulse rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-10 w-full bg-muted animate-pulse rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n            lineNumber: 398,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!patient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"Paciente n\\xe3o encontrado.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                    lineNumber: 448,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    onClick: ()=>router.back(),\n                    className: \"mt-4\",\n                    children: \"Voltar\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                    lineNumber: 449,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n            lineNumber: 447,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: patient.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: [\n                                    \"Paciente desde \",\n                                    (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateBR)(patient.created_at)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleSavePatient,\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Salvar\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>setEditMode(false),\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Cancelar\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setEditMode(true),\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Editar\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>router.back(),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: \"Voltar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                lineNumber: 458,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                defaultValue: \"dados\",\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                        className: \"grid w-full grid-cols-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"dados\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Dados Principais\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"consultas\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Hist\\xf3rico de Consultas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"anexos\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Anexos\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"dados\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"Informa\\xe7\\xf5es Pessoais\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Dados b\\xe1sicos do paciente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"name\",\n                                                        children: \"Nome Completo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"name\",\n                                                        value: editForm.name,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                name: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: patient.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"birth_date\",\n                                                        children: \"Data de Nascimento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"birth_date\",\n                                                        type: \"date\",\n                                                        value: editForm.birth_date,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                birth_date: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: patient.birth_date ? \"\".concat((0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateBR)(patient.birth_date), \" (\").concat(calculateAge(patient.birth_date), \")\") : 'Não informado'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"cpf\",\n                                                        children: \"CPF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"cpf\",\n                                                        value: editForm.cpf,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                cpf: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: patient.cpf || 'Não informado'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"phone\",\n                                                        children: \"Telefone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"phone\",\n                                                        value: editForm.phone,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                phone: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: patient.phone || 'Não informado'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"email\",\n                                                        children: \"E-mail\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"email\",\n                                                        type: \"email\",\n                                                        value: editForm.email,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                email: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: patient.email || 'Não informado'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"address\",\n                                                        children: \"Endere\\xe7o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                        id: \"address\",\n                                                        value: editForm.address,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                address: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4 text-muted-foreground mt-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: patient.address || 'Não informado'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"notes\",\n                                                        children: \"Observa\\xe7\\xf5es\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                        id: \"notes\",\n                                                        value: editForm.notes,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                notes: e.target.value\n                                                            }),\n                                                        rows: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: patient.notes || 'Nenhuma observação'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"consultas\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"Hist\\xf3rico de Consultas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: [\n                                                appointments.length,\n                                                \" consulta(s) registrada(s)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: appointments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Nenhuma consulta registrada para este paciente\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: appointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 rounded-lg border bg-card/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateBR)(appointment.start_time)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 651,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatTimeBR)(appointment.start_time)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium\",\n                                                                        children: appointment.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 659,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.getAppointmentTypeBR)(appointment.type)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 660,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: appointment.healthcare_professional_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 664,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: appointment.status === 'in_progress' ? 'default' : appointment.status === 'completed' ? 'secondary' : appointment.status === 'cancelled' ? 'destructive' : 'outline',\n                                                                children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.getAppointmentStatusBR)(appointment.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            appointment.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: [\n                                                                    \"R$ \",\n                                                                    appointment.price.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            canViewMedicalRecords && !permissionsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenMedicalRecord(appointment),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    \"Prontu\\xe1rio\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, appointment.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 636,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"anexos\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        children: \"Anexos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        children: [\n                                                            attachments.length,\n                                                            \" arquivo(s) anexado(s)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                open: uploadDialogOpen,\n                                                onOpenChange: setUploadDialogOpen,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 716,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Adicionar Arquivo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                        children: \"Enviar Arquivo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 722,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                                                        children: \"Selecione um arquivo para anexar ao prontu\\xe1rio do paciente\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 723,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 721,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                            htmlFor: \"file\",\n                                                                            children: \"Arquivo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 729,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            id: \"file\",\n                                                                            type: \"file\",\n                                                                            onChange: (e)=>{\n                                                                                var _e_target_files;\n                                                                                return setSelectedFile(((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 728,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogFooter, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>setUploadDialogOpen(false),\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 738,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        onClick: handleFileUpload,\n                                                                        disabled: !selectedFile,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 742,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \"Enviar\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 741,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 706,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 705,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: attachments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Nenhum arquivo anexado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 rounded-lg border bg-card/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-8 w-8 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium\",\n                                                                        children: attachment.file_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 766,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            formatFileSize(attachment.file_size),\n                                                                            \" • \",\n                                                                            (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateTimeBR)(attachment.created_at)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 767,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDownloadAttachment(attachment.id, attachment.file_name),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 778,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \"Baixar\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 773,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"destructive\",\n                                                                onClick: ()=>handleDeleteAttachment(attachment.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 786,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, attachment.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 750,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 704,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 703,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                lineNumber: 490,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: medicalRecordOpen,\n                onOpenChange: setMedicalRecordOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setMedicalRecordOpen(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                children: \"Prontu\\xe1rio M\\xe9dico\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 811,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                                children: selectedAppointment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        selectedAppointment.title,\n                                                        \" - \",\n                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateTimeBR)(selectedAppointment.start_time)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 802,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 801,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                selectedAppointment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: \"Informa\\xe7\\xf5es da Consulta\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 828,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Data\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateBR)(selectedAppointment.start_time)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Hor\\xe1rio\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 837,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: [\n                                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatTimeBR)(selectedAppointment.start_time),\n                                                                        \" - \",\n                                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatTimeBR)(selectedAppointment.end_time)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 838,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 836,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Tipo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.getAppointmentTypeBR)(selectedAppointment.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 842,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 840,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 845,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: selectedAppointment.status === 'confirmed' ? 'default' : selectedAppointment.status === 'completed' ? 'secondary' : selectedAppointment.status === 'cancelled' ? 'destructive' : selectedAppointment.status === 'in_progress' ? 'default' : 'outline',\n                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.getAppointmentStatusBR)(selectedAppointment.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                selectedAppointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Profissional\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 859,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: selectedAppointment.healthcare_professional_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 860,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 858,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                selectedAppointment.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Descri\\xe7\\xe3o\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 865,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: selectedAppointment.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 866,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 864,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 830,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 15\n                                }, undefined),\n                                selectedAppointment && selectedAppointment.status === 'confirmed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"pt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleStartTreatment,\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 878,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Iniciar Atendimento\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 877,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Hist\\xf3rico de Registros\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 888,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Registros m\\xe9dicos desta consulta\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 887,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: medicalRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Nenhum registro m\\xe9dico encontrado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 895,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: medicalRecords.map((record, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-l-4 border-primary pl-4 py-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateTimeBR)(record.created_at)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 904,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs\",\n                                                                        children: record.created_by_name || 'Sistema'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 907,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground whitespace-pre-wrap\",\n                                                                children: record.notes\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 911,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 893,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 886,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedAppointment && (selectedAppointment.status === 'in_progress' || selectedAppointment.status === 'confirmed') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Adicionar Registro\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 925,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Adicione observa\\xe7\\xf5es e anota\\xe7\\xf5es sobre esta consulta\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 926,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                    placeholder: \"Digite suas observa\\xe7\\xf5es sobre a consulta...\",\n                                                    value: newRecord,\n                                                    onChange: (e)=>setNewRecord(e.target.value),\n                                                    rows: 4\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 931,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    onClick: handleAddMedicalRecord,\n                                                    disabled: !newRecord.trim(),\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 942,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Adicionar Registro\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 930,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 823,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                    lineNumber: 800,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                lineNumber: 799,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n        lineNumber: 457,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PatientDetailPage, \"28Ve0kI8iEad7rDZbN4j1x028MM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_14__.usePermissions\n    ];\n});\n_c = PatientDetailPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PatientDetailPage);\nvar _c;\n$RefreshReg$(_c, \"PatientDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/pacientes/[id]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/usePermissions.tsx":
/*!**************************************!*\
  !*** ./src/hooks/usePermissions.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PermissionGate: () => (/* binding */ PermissionGate),\n/* harmony export */   PermissionsProvider: () => (/* binding */ PermissionsProvider),\n/* harmony export */   RoleGate: () => (/* binding */ RoleGate),\n/* harmony export */   usePermissions: () => (/* binding */ usePermissions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ PermissionsProvider,usePermissions,PermissionGate,RoleGate auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\nconst PermissionsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction PermissionsProvider(param) {\n    let { children } = param;\n    _s();\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchPermissions = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch user roles\n            const rolesResponse = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.makeAuthenticatedRequest)('/api/user-roles');\n            if (rolesResponse.ok) {\n                const rolesData = await rolesResponse.json();\n                const userRoles = (rolesData.data || []).map((role)=>role.role_name);\n                setRoles(userRoles);\n            }\n            // For now, give admin permissions to all users since we don't have the permissions table\n            const mockPermissions = [\n                {\n                    resource: 'patients',\n                    action: 'read'\n                },\n                {\n                    resource: 'patients',\n                    action: 'create'\n                },\n                {\n                    resource: 'patients',\n                    action: 'update'\n                },\n                {\n                    resource: 'patients',\n                    action: 'delete'\n                },\n                {\n                    resource: 'appointments',\n                    action: 'read'\n                },\n                {\n                    resource: 'appointments',\n                    action: 'create'\n                },\n                {\n                    resource: 'appointments',\n                    action: 'update'\n                },\n                {\n                    resource: 'appointments',\n                    action: 'delete'\n                },\n                {\n                    resource: 'procedures',\n                    action: 'read'\n                },\n                {\n                    resource: 'procedures',\n                    action: 'create'\n                },\n                {\n                    resource: 'procedures',\n                    action: 'update'\n                },\n                {\n                    resource: 'procedures',\n                    action: 'delete'\n                },\n                {\n                    resource: 'healthcare_professionals',\n                    action: 'read'\n                },\n                {\n                    resource: 'healthcare_professionals',\n                    action: 'create'\n                },\n                {\n                    resource: 'healthcare_professionals',\n                    action: 'update'\n                },\n                {\n                    resource: 'healthcare_professionals',\n                    action: 'delete'\n                }\n            ];\n            setPermissions(mockPermissions);\n        } catch (error) {\n            console.error('Error fetching permissions:', error);\n            setRoles([]);\n            setPermissions([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PermissionsProvider.useEffect\": ()=>{\n            fetchPermissions();\n        }\n    }[\"PermissionsProvider.useEffect\"], []);\n    const hasRole = (role)=>roles.includes(role);\n    const isAdmin = hasRole('admin');\n    const hasPermission = (resource, action)=>{\n        return permissions.some((p)=>p.resource === resource && p.action === action);\n    };\n    const value = {\n        roles,\n        permissions,\n        loading,\n        isAdmin,\n        hasRole,\n        hasPermission,\n        refetch: fetchPermissions\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PermissionsContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\hooks\\\\usePermissions.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(PermissionsProvider, \"8JtdMUGb/4VnWiYnqU1rdFGg3Fo=\");\n_c = PermissionsProvider;\nfunction usePermissions() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PermissionsContext);\n    if (context === undefined) {\n        throw new Error('usePermissions must be used within a PermissionsProvider');\n    }\n    return context;\n}\n_s1(usePermissions, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction PermissionGate(param) {\n    let { resource, action, children, fallback = null } = param;\n    _s2();\n    const { hasPermission, loading } = usePermissions();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\hooks\\\\usePermissions.tsx\",\n            lineNumber: 121,\n            columnNumber: 12\n        }, this);\n    }\n    if (!hasPermission(resource, action)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s2(PermissionGate, \"I8ZLvNYd86HqkTuwVJiHqmAj4AQ=\", false, function() {\n    return [\n        usePermissions\n    ];\n});\n_c1 = PermissionGate;\nfunction RoleGate(param) {\n    let { roles, children, fallback = null, requireAll = false } = param;\n    _s3();\n    const { hasRole, loading } = usePermissions();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\hooks\\\\usePermissions.tsx\",\n            lineNumber: 143,\n            columnNumber: 12\n        }, this);\n    }\n    const hasRequiredRoles = requireAll ? roles.every((role)=>hasRole(role)) : roles.some((role)=>hasRole(role));\n    if (!hasRequiredRoles) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s3(RoleGate, \"1zy/6MRlrkUdPp9xxK6TBUXi3aI=\", false, function() {\n    return [\n        usePermissions\n    ];\n});\n_c2 = RoleGate;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PermissionsProvider\");\n$RefreshReg$(_c1, \"PermissionGate\");\n$RefreshReg$(_c2, \"RoleGate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/usePermissions.tsx\n"));

/***/ })

});