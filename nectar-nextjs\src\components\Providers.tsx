"use client"

import { To<PERSON> } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ReactQueryProvider } from "@/components/ReactQueryProvider";
import { PermissionsProvider } from "@/hooks/usePermissions";
import { AuthProvider } from "@/hooks/useAuth";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ReactQueryProvider>
      <TooltipProvider>
        <AuthProvider>
          <PermissionsProvider>
            {children}
            <Toaster />
            <Sonner />
          </PermissionsProvider>
        </AuthProvider>
      </TooltipProvider>
    </ReactQueryProvider>
  );
}
