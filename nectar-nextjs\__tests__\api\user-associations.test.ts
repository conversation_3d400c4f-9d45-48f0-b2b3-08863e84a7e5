/**
 * @jest-environment node
 */
import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/user-associations/route'

// Mock the auth utilities
jest.mock('@/lib/api-utils', () => ({
  withAuth: jest.fn((request, callback) => {
    // Mock authenticated user (admin)
    const mockUserId = '04294899-adc0-4cd7-90d6-bf23617bf8fa'
    const mockSupabase = {
      from: jest.fn(() => ({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn(() => ({
              data: { role: 'admin' },
              error: null
            })),
            order: jest.fn(() => ({
              data: [],
              error: null
            }))
          }))
        })),
        insert: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn(() => ({
              data: {
                id: 'test-association-id',
                accessor_user_id: 'c4d5fe2c-b2ca-47da-9ffc-7e02237a919e',
                target_user_id: 'f8a54e36-d6d4-4a74-a0cc-383cffee47d3',
                association_type: 'secretary_to_doctor',
                permissions: null,
                is_active: true,
                created_by: mockUserId,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                accessor_user: {
                  id: 'c4d5fe2c-b2ca-47da-9ffc-7e02237a919e',
                  name: 'Teste',
                  email: '<EMAIL>',
                  role: 'secretary'
                },
                target_user: {
                  id: 'f8a54e36-d6d4-4a74-a0cc-383cffee47d3',
                  name: 'Erico Cunha',
                  email: '<EMAIL>',
                  role: 'doctor'
                },
                created_by_user: {
                  id: mockUserId,
                  name: 'Ciro Romanel',
                  email: '<EMAIL>'
                }
              },
              error: null
            }))
          }))
        }))
      }))
    }
    return callback(mockUserId, mockSupabase)
  }),
  createApiResponse: jest.fn((data, message, status) => 
    new Response(JSON.stringify({ data, message }), { status: status || 200 })
  ),
  handleApiError: jest.fn((error) => 
    new Response(JSON.stringify({ error: error.message }), { status: 500 })
  )
}))

describe('/api/user-associations', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET', () => {
    it('should return empty associations list', async () => {
      const request = new NextRequest('http://localhost:3000/api/user-associations')
      const response = await GET(request)

      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.data).toEqual([])
    })

    it('should filter associations by accessor_user_id', async () => {
      const request = new NextRequest('http://localhost:3000/api/user-associations?accessor_user_id=test-user-id')
      const response = await GET(request)

      expect(response.status).toBe(200)
    })
  })

  describe('POST', () => {
    it('should create a secretary_to_doctor association', async () => {
      const associationData = {
        accessor_user_id: 'c4d5fe2c-b2ca-47da-9ffc-7e02237a919e',
        target_user_id: 'f8a54e36-d6d4-4a74-a0cc-383cffee47d3',
        association_type: 'secretary_to_doctor',
        permissions: {
          appointments: ['read', 'create'],
          patients: ['read']
        }
      }

      const request = new NextRequest('http://localhost:3000/api/user-associations', {
        method: 'POST',
        body: JSON.stringify(associationData),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      
      expect(response.status).toBe(201)
      const data = await response.json()
      expect(data.data.association_type).toBe('secretary_to_doctor')
      expect(data.message).toBe('Association created successfully')
    })

    it('should validate required fields', async () => {
      const invalidData = {
        accessor_user_id: '',
        target_user_id: 'f8a54e36-d6d4-4a74-a0cc-383cffee47d3',
        association_type: 'secretary_to_doctor'
      }

      const request = new NextRequest('http://localhost:3000/api/user-associations', {
        method: 'POST',
        body: JSON.stringify(invalidData),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      
      expect(response.status).toBe(400)
      const data = await response.json()
      expect(data.message).toContain('required')
    })

    it('should validate association type', async () => {
      const invalidData = {
        accessor_user_id: 'c4d5fe2c-b2ca-47da-9ffc-7e02237a919e',
        target_user_id: 'f8a54e36-d6d4-4a74-a0cc-383cffee47d3',
        association_type: 'invalid_type'
      }

      const request = new NextRequest('http://localhost:3000/api/user-associations', {
        method: 'POST',
        body: JSON.stringify(invalidData),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      
      expect(response.status).toBe(400)
      const data = await response.json()
      expect(data.message).toContain('Invalid association_type')
    })
  })
})
