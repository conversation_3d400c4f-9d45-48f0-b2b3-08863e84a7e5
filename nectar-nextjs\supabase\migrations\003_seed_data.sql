-- Insert default roles and permissions
INSERT INTO public.permissions (role_name, resource, action) VALUES
-- Admin permissions (full access)
('admin', 'patients', 'create'),
('admin', 'patients', 'read'),
('admin', 'patients', 'update'),
('admin', 'patients', 'delete'),
('admin', 'appointments', 'create'),
('admin', 'appointments', 'read'),
('admin', 'appointments', 'update'),
('admin', 'appointments', 'delete'),
('admin', 'procedures', 'create'),
('admin', 'procedures', 'read'),
('admin', 'procedures', 'update'),
('admin', 'procedures', 'delete'),
('admin', 'healthcare_professionals', 'create'),
('admin', 'healthcare_professionals', 'read'),
('admin', 'healthcare_professionals', 'update'),
('admin', 'healthcare_professionals', 'delete'),
('admin', 'settings', 'read'),
('admin', 'settings', 'update'),
('admin', 'users', 'read'),
('admin', 'users', 'update'),
('admin', 'roles', 'create'),
('admin', 'roles', 'read'),
('admin', 'roles', 'update'),
('admin', 'roles', 'delete'),

-- Doctor permissions
('doctor', 'patients', 'create'),
('doctor', 'patients', 'read'),
('doctor', 'patients', 'update'),
('doctor', 'appointments', 'create'),
('doctor', 'appointments', 'read'),
('doctor', 'appointments', 'update'),
('doctor', 'procedures', 'read'),
('doctor', 'healthcare_professionals', 'read'),

-- Secretary permissions
('secretary', 'patients', 'create'),
('secretary', 'patients', 'read'),
('secretary', 'patients', 'update'),
('secretary', 'appointments', 'create'),
('secretary', 'appointments', 'read'),
('secretary', 'appointments', 'update'),
('secretary', 'appointments', 'delete'),
('secretary', 'procedures', 'read'),
('secretary', 'healthcare_professionals', 'read'),

-- Assistant permissions
('assistant', 'patients', 'read'),
('assistant', 'appointments', 'read'),
('assistant', 'procedures', 'read'),
('assistant', 'healthcare_professionals', 'read')

ON CONFLICT (role_name, resource, action) DO NOTHING;

-- Insert default procedures (common medical procedures)
-- Note: These will be inserted for each user when they sign up
-- For now, we'll create a template that can be copied

-- Create a function to insert default procedures for new users
CREATE OR REPLACE FUNCTION public.create_default_procedures_for_user(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
  INSERT INTO public.procedures (user_id, name, description, default_price, duration_minutes) VALUES
  (user_uuid, 'Consulta Médica', 'Consulta médica de rotina', 150.00, 30),
  (user_uuid, 'Consulta de Retorno', 'Consulta de retorno/acompanhamento', 100.00, 20),
  (user_uuid, 'Exame Físico Completo', 'Exame físico detalhado', 200.00, 45),
  (user_uuid, 'Consulta de Emergência', 'Atendimento de emergência', 300.00, 60),
  (user_uuid, 'Avaliação Pré-Operatória', 'Avaliação médica pré-cirúrgica', 180.00, 40),
  (user_uuid, 'Consulta Especializada', 'Consulta com especialista', 250.00, 45),
  (user_uuid, 'Acompanhamento Pós-Operatório', 'Consulta pós-cirúrgica', 120.00, 25),
  (user_uuid, 'Consulta Preventiva', 'Consulta de medicina preventiva', 130.00, 30),
  (user_uuid, 'Avaliação Geriátrica', 'Consulta especializada para idosos', 200.00, 50),
  (user_uuid, 'Consulta Pediátrica', 'Consulta médica infantil', 160.00, 35);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to insert default healthcare professionals for new users
CREATE OR REPLACE FUNCTION public.create_default_healthcare_professionals_for_user(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
  INSERT INTO public.healthcare_professionals (user_id, name, specialty, is_active) VALUES
  (user_uuid, 'Dr. João Silva', 'Clínico Geral', true),
  (user_uuid, 'Dra. Maria Santos', 'Cardiologia', true),
  (user_uuid, 'Dr. Pedro Oliveira', 'Pediatria', true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to insert default clinic settings for new users
CREATE OR REPLACE FUNCTION public.create_default_clinic_settings_for_user(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
  INSERT INTO public.clinic_settings (user_id, setting_key, setting_value) VALUES
  (user_uuid, 'business_hours', '{
    "monday": {"start": "08:00", "end": "18:00", "enabled": true},
    "tuesday": {"start": "08:00", "end": "18:00", "enabled": true},
    "wednesday": {"start": "08:00", "end": "18:00", "enabled": true},
    "thursday": {"start": "08:00", "end": "18:00", "enabled": true},
    "friday": {"start": "08:00", "end": "18:00", "enabled": true},
    "saturday": {"start": "08:00", "end": "12:00", "enabled": false},
    "sunday": {"start": "08:00", "end": "12:00", "enabled": false}
  }'::jsonb),
  (user_uuid, 'appointment_duration', '{"default_minutes": 30}'::jsonb),
  (user_uuid, 'timezone', '{"timezone": "America/Sao_Paulo"}'::jsonb),
  (user_uuid, 'notifications', '{
    "email_appointments": true,
    "sms_reminders": false,
    "push_notifications": true,
    "reminder_hours": 24
  }'::jsonb),
  (user_uuid, 'clinic_info', '{
    "name": "Minha Clínica",
    "address": "",
    "phone": "",
    "email": "",
    "website": ""
  }'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the handle_new_user function to include default data creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Create user profile
  INSERT INTO public.users (id, email, name)
  VALUES (NEW.id, NEW.email, COALESCE(NEW.raw_user_meta_data->>'name', NEW.email));
  
  -- Assign default role (admin for first user, doctor for others)
  INSERT INTO public.user_roles (user_id, role_name)
  VALUES (NEW.id, 'admin');
  
  -- Create default procedures
  PERFORM public.create_default_procedures_for_user(NEW.id);
  
  -- Create default healthcare professionals
  PERFORM public.create_default_healthcare_professionals_for_user(NEW.id);
  
  -- Create default clinic settings
  PERFORM public.create_default_clinic_settings_for_user(NEW.id);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_patients_user_id ON public.patients(user_id);
CREATE INDEX IF NOT EXISTS idx_patients_name ON public.patients(name);
CREATE INDEX IF NOT EXISTS idx_patients_email ON public.patients(email);

CREATE INDEX IF NOT EXISTS idx_appointments_user_id ON public.appointments(user_id);
CREATE INDEX IF NOT EXISTS idx_appointments_patient_id ON public.appointments(patient_id);
CREATE INDEX IF NOT EXISTS idx_appointments_professional_id ON public.appointments(healthcare_professional_id);
CREATE INDEX IF NOT EXISTS idx_appointments_start_time ON public.appointments(start_time);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON public.appointments(status);

CREATE INDEX IF NOT EXISTS idx_healthcare_professionals_user_id ON public.healthcare_professionals(user_id);
CREATE INDEX IF NOT EXISTS idx_healthcare_professionals_active ON public.healthcare_professionals(is_active);

CREATE INDEX IF NOT EXISTS idx_procedures_user_id ON public.procedures(user_id);
CREATE INDEX IF NOT EXISTS idx_procedures_name ON public.procedures(name);

CREATE INDEX IF NOT EXISTS idx_appointment_procedures_appointment_id ON public.appointment_procedures(appointment_id);
CREATE INDEX IF NOT EXISTS idx_appointment_procedures_procedure_id ON public.appointment_procedures(procedure_id);

CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON public.user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_name ON public.user_roles(role_name);

CREATE INDEX IF NOT EXISTS idx_permissions_role_name ON public.permissions(role_name);
CREATE INDEX IF NOT EXISTS idx_permissions_resource_action ON public.permissions(resource, action);

CREATE INDEX IF NOT EXISTS idx_clinic_settings_user_id ON public.clinic_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_clinic_settings_key ON public.clinic_settings(setting_key);

CREATE INDEX IF NOT EXISTS idx_patient_attachments_patient_id ON public.patient_attachments(patient_id);

-- Create views for easier data access
CREATE OR REPLACE VIEW public.appointments_with_details AS
SELECT 
  a.id,
  a.user_id,
  a.title,
  a.description,
  a.start_time,
  a.end_time,
  a.type,
  a.status,
  a.price,
  a.notes,
  a.patient_id,
  p.name as patient_name,
  p.email as patient_email,
  p.phone as patient_phone,
  a.healthcare_professional_id,
  hp.name as healthcare_professional_name,
  hp.specialty as healthcare_professional_specialty,
  a.created_at,
  a.updated_at
FROM public.appointments a
INNER JOIN public.patients p ON p.id = a.patient_id
LEFT JOIN public.healthcare_professionals hp ON hp.id = a.healthcare_professional_id;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
