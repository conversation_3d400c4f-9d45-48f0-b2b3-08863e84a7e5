import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type HealthcareProfessional = Tables<'healthcare_professionals'>
type HealthcareProfessionalInsert = TablesInsert<'healthcare_professionals'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Get accessible users first
      const { data: accessibleUsers, error: accessError } = await supabase
        .rpc('get_accessible_users', { current_user_id: userId })

      if (accessError) {
        return handleApiError(accessError)
      }

      const accessibleUserIds = accessibleUsers?.map((user: any) => user.user_id) || []

      if (accessibleUserIds.length === 0) {
        return createApiResponse([])
      }

      const { data: professionals, error } = await supabase
        .from('healthcare_professionals')
        .select('*')
        .in('user_id', accessibleUserIds)
        .order('name')

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(professionals || [])
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      
      const professionalData: HealthcareProfessionalInsert = {
        ...body,
        user_id: userId,
        is_active: body.is_active ?? true
      }

      const { data: professional, error } = await supabase
        .from('healthcare_professionals')
        .insert(professionalData)
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(professional, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
