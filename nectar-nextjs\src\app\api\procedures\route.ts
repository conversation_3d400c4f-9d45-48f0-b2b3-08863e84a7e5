import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type Procedure = Tables<'procedures'>
type ProcedureInsert = TablesInsert<'procedures'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Get accessible users first
      const { data: accessibleUsers, error: accessError } = await supabase
        .rpc('get_accessible_users', { current_user_id: userId })

      if (accessError) {
        return handleApiError(accessError)
      }

      const accessibleUserIds = accessibleUsers?.map((user: any) => user.user_id) || []

      if (accessibleUserIds.length === 0) {
        return createApiResponse([])
      }

      const { data: procedures, error } = await supabase
        .from('procedures')
        .select('*')
        .in('user_id', accessibleUserIds)
        .order('name')

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(procedures || [])
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      
      const procedureData: ProcedureInsert = {
        ...body,
        user_id: userId,
        default_price: body.default_price ? parseFloat(body.default_price) : null,
        duration_minutes: body.duration_minutes ? parseInt(body.duration_minutes) : null
      }

      const { data: procedure, error } = await supabase
        .from('procedures')
        .insert(procedureData)
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(procedure, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
