import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/supabase'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for server-side operations
const supabaseAdmin = createClient<Database>(supabaseUrl, supabaseServiceKey)

export type Permission = {
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete';
};

export type Role = 'admin' | 'doctor' | 'secretary' | 'assistant';

/**
 * Check if a user has a specific permission
 */
export async function hasPermission(
  userId: string, 
  resource: string, 
  action: 'create' | 'read' | 'update' | 'delete'
): Promise<boolean> {
  try {
    // Get user roles
    const { data: userRoles, error: rolesError } = await supabaseAdmin
      .from('user_roles')
      .select('role_name')
      .eq('user_id', userId)

    if (rolesError || !userRoles || userRoles.length === 0) {
      return false
    }

    // Check if any of the user's roles have the required permission
    const roleNames = userRoles.map(role => role.role_name)
    
    const { data: permissions, error: permissionsError } = await supabaseAdmin
      .from('permissions')
      .select('*')
      .in('role_name', roleNames)
      .eq('resource', resource)
      .eq('action', action)

    if (permissionsError) {
      console.error('Error checking permissions:', permissionsError)
      return false
    }

    return permissions && permissions.length > 0
  } catch (error) {
    console.error('Error in hasPermission:', error)
    return false
  }
}

/**
 * Check if a user has any of the specified roles
 */
export async function hasRole(userId: string, roles: Role[]): Promise<boolean> {
  try {
    const { data: userRoles, error } = await supabaseAdmin
      .from('user_roles')
      .select('role_name')
      .eq('user_id', userId)
      .in('role_name', roles)

    if (error) {
      console.error('Error checking roles:', error)
      return false
    }

    return userRoles && userRoles.length > 0
  } catch (error) {
    console.error('Error in hasRole:', error)
    return false
  }
}

/**
 * Get all permissions for a user
 */
export async function getUserPermissions(userId: string): Promise<Permission[]> {
  try {
    // Get user roles
    const { data: userRoles, error: rolesError } = await supabaseAdmin
      .from('user_roles')
      .select('role_name')
      .eq('user_id', userId)

    if (rolesError || !userRoles || userRoles.length === 0) {
      return []
    }

    // Get all permissions for these roles
    const roleNames = userRoles.map(role => role.role_name)
    
    const { data: permissions, error: permissionsError } = await supabaseAdmin
      .from('permissions')
      .select('resource, action')
      .in('role_name', roleNames)

    if (permissionsError) {
      console.error('Error getting user permissions:', permissionsError)
      return []
    }

    return permissions || []
  } catch (error) {
    console.error('Error in getUserPermissions:', error)
    return []
  }
}

/**
 * Check if a user is an admin
 */
export async function isAdmin(userId: string): Promise<boolean> {
  return hasRole(userId, ['admin'])
}

/**
 * Middleware function to check permissions for API routes
 */
export function requirePermission(resource: string, action: 'create' | 'read' | 'update' | 'delete') {
  return async (userId: string): Promise<boolean> => {
    return hasPermission(userId, resource, action)
  }
}

/**
 * Middleware function to check roles for API routes
 */
export function requireRole(roles: Role[]) {
  return async (userId: string): Promise<boolean> => {
    return hasRole(userId, roles)
  }
}

/**
 * Default permissions for each role
 */
export const DEFAULT_PERMISSIONS: Record<Role, Permission[]> = {
  admin: [
    // Full access to everything
    { resource: 'appointments', action: 'create' },
    { resource: 'appointments', action: 'read' },
    { resource: 'appointments', action: 'update' },
    { resource: 'appointments', action: 'delete' },
    { resource: 'patients', action: 'create' },
    { resource: 'patients', action: 'read' },
    { resource: 'patients', action: 'update' },
    { resource: 'patients', action: 'delete' },
    { resource: 'medical_records', action: 'create' },
    { resource: 'medical_records', action: 'read' },
    { resource: 'medical_records', action: 'update' },
    { resource: 'medical_records', action: 'delete' },
    { resource: 'procedures', action: 'create' },
    { resource: 'procedures', action: 'read' },
    { resource: 'procedures', action: 'update' },
    { resource: 'procedures', action: 'delete' },
    { resource: 'settings', action: 'read' },
    { resource: 'settings', action: 'update' },
  ],
  doctor: [
    // Can manage appointments and patients, read procedures
    { resource: 'appointments', action: 'create' },
    { resource: 'appointments', action: 'read' },
    { resource: 'appointments', action: 'update' },
    { resource: 'patients', action: 'create' },
    { resource: 'patients', action: 'read' },
    { resource: 'patients', action: 'update' },
    { resource: 'medical_records', action: 'create' },
    { resource: 'medical_records', action: 'read' },
    { resource: 'medical_records', action: 'update' },
    { resource: 'procedures', action: 'read' },
  ],
  secretary: [
    // Can manage appointments and patients, read procedures
    { resource: 'appointments', action: 'create' },
    { resource: 'appointments', action: 'read' },
    { resource: 'appointments', action: 'update' },
    { resource: 'patients', action: 'create' },
    { resource: 'patients', action: 'read' },
    { resource: 'patients', action: 'update' },
    { resource: 'procedures', action: 'read' },
  ],
  assistant: [
    // Read-only access to appointments and patients
    { resource: 'appointments', action: 'read' },
    { resource: 'patients', action: 'read' },
  ],
};
