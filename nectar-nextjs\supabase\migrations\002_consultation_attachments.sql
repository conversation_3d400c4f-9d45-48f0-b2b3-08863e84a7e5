-- Create consultation_attachments table
CREATE TABLE IF NOT EXISTS consultation_attachments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  appointment_id UUID NOT NULL REFERENCES appointments(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  file_type TEXT NOT NULL,
  uploaded_by UUID NOT NULL REFERENCES auth.users(id),
  uploaded_by_name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_consultation_attachments_appointment_id ON consultation_attachments(appointment_id);
CREATE INDEX IF NOT EXISTS idx_consultation_attachments_user_id ON consultation_attachments(user_id);
CREATE INDEX IF NOT EXISTS idx_consultation_attachments_created_at ON consultation_attachments(created_at DESC);

-- Enable RLS
ALTER TABLE consultation_attachments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own consultation attachments" ON consultation_attachments
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own consultation attachments" ON consultation_attachments
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own consultation attachments" ON consultation_attachments
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own consultation attachments" ON consultation_attachments
  FOR DELETE USING (user_id = auth.uid());

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_consultation_attachments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_consultation_attachments_updated_at
  BEFORE UPDATE ON consultation_attachments
  FOR EACH ROW
  EXECUTE FUNCTION update_consultation_attachments_updated_at();

-- Create storage bucket for consultation attachments if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('consultation-attachments', 'consultation-attachments', false)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies
CREATE POLICY "Users can upload consultation attachments" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'consultation-attachments' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view their consultation attachments" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'consultation-attachments' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their consultation attachments" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'consultation-attachments' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their consultation attachments" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'consultation-attachments' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );
