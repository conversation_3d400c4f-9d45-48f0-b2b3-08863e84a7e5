import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import { createAdminClient } from '@/lib/supabase/admin'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Check if user is admin
      const { data: userRoles, error: rolesError } = await supabase
        .from('user_roles')
        .select('role_name')
        .eq('user_id', userId)

      if (rolesError) {
        return handleApiError(rolesError)
      }

      const isAdmin = userRoles?.some(role => role.role_name === 'admin')
      if (!isAdmin) {
        return createApiResponse(null, 'Forbidden', 403)
      }

      // Get all users from public.users table
      const { data: users, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(users || [])
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Check if user is admin
      const { data: userRoles, error: rolesError } = await supabase
        .from('user_roles')
        .select('role_name')
        .eq('user_id', userId)

      if (rolesError) {
        return handleApiError(rolesError)
      }

      const isAdmin = userRoles?.some(role => role.role_name === 'admin')
      if (!isAdmin) {
        return createApiResponse(null, 'Forbidden', 403)
      }

      const body = await request.json()
      const { email, name, phone, role, password, is_active = true } = body

      if (!email || !password || !name) {
        return createApiResponse(null, 'Email, password, and name are required', 400)
      }

      // Create user in auth.users using Supabase Admin API with service role
      const adminClient = createAdminClient()

      console.log('Creating user with data:', { email, role, name, phone })

      const { data: authUser, error: authError } = await adminClient.auth.admin.createUser({
        email,
        password,
        email_confirm: true, // Auto-confirm email
        user_metadata: {
          name,
          phone,
          role: role || 'user'
        }
      })

      if (authError) {
        console.error('Auth user creation error:', authError)
        return createApiResponse(null, `Failed to create user: ${authError.message}`, 400)
      }

      if (!authUser.user) {
        return createApiResponse(null, 'Failed to create user', 400)
      }

      // The triggers will automatically:
      // 1. Sync auth.users to public.users
      // 2. Create user_roles entry
      // 3. Auto-populate healthcare_professionals or secretaries table

      // Wait a moment for triggers to complete
      await new Promise(resolve => setTimeout(resolve, 100))

      // Fetch the created user from public.users
      const { data: publicUser, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .eq('id', authUser.user.id)
        .single()

      if (fetchError) {
        console.error('Error fetching created user:', fetchError)
        // User was created in auth but not synced properly
        return createApiResponse({
          id: authUser.user.id,
          email: authUser.user.email,
          name,
          phone,
          role: role || 'user',
          is_active
        }, 'User created successfully', 201)
      }

      return createApiResponse(publicUser, 'User created successfully', 201)
    } catch (error) {
      console.error('User creation error:', error)
      return handleApiError(error)
    }
  })
}
