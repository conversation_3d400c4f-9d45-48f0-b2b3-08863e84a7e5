/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user-associations/route";
exports.ids = ["app/api/user-associations/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-associations%2Froute&page=%2Fapi%2Fuser-associations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-associations%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-associations%2Froute&page=%2Fapi%2Fuser-associations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-associations%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_cirov_Documents_next_js_nectar_nectar_nextjs_src_app_api_user_associations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/user-associations/route.ts */ \"(rsc)/./src/app/api/user-associations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user-associations/route\",\n        pathname: \"/api/user-associations\",\n        filename: \"route\",\n        bundlePath: \"app/api/user-associations/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\api\\\\user-associations\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_cirov_Documents_next_js_nectar_nectar_nextjs_src_app_api_user_associations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-associations%2Froute&page=%2Fapi%2Fuser-associations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-associations%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/user-associations/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/user-associations/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./src/lib/api-utils.ts\");\n\nasync function GET(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withAuth)(request, async (userId, supabase)=>{\n        try {\n            const { searchParams } = new URL(request.url);\n            const accessorUserId = searchParams.get('accessor_user_id');\n            const targetUserId = searchParams.get('target_user_id');\n            const associationType = searchParams.get('association_type');\n            let query = supabase.from('user_associations').select(`\n          *,\n          accessor_user:users!accessor_user_id(id, name, email, role),\n          target_user:users!target_user_id(id, name, email, role),\n          created_by_user:users!created_by(id, name, email)\n        `).eq('is_active', true).order('created_at', {\n                ascending: false\n            });\n            // Apply filters\n            if (accessorUserId) {\n                query = query.eq('accessor_user_id', accessorUserId);\n            }\n            if (targetUserId) {\n                query = query.eq('target_user_id', targetUserId);\n            }\n            if (associationType) {\n                query = query.eq('association_type', associationType);\n            }\n            const { data: associations, error } = await query;\n            if (error) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n            }\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(associations || []);\n        } catch (error) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n        }\n    });\n}\nasync function POST(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withAuth)(request, async (userId, supabase)=>{\n        try {\n            // Check if user is admin or involved in the association\n            const { data: userRoles, error: rolesError } = await supabase.from('user_roles').select('role_name').eq('user_id', userId);\n            if (rolesError) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(rolesError);\n            }\n            const isAdmin = userRoles?.some((role)=>role.role_name === 'admin');\n            const body = await request.json();\n            const { accessor_user_id, target_user_id, association_type, permissions } = body;\n            if (!accessor_user_id || !target_user_id || !association_type) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(null, 'accessor_user_id, target_user_id, and association_type are required', 400);\n            }\n            // Validate association type\n            if (![\n                'secretary_to_doctor',\n                'doctor_to_doctor'\n            ].includes(association_type)) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(null, 'Invalid association_type', 400);\n            }\n            // Check if user can create this association\n            if (!isAdmin && userId !== accessor_user_id && userId !== target_user_id) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(null, 'You can only create associations you are part of', 403);\n            }\n            // Validate user roles for association type\n            if (association_type === 'secretary_to_doctor') {\n                // Check if accessor is secretary and target is doctor\n                const { data: accessorRoles } = await supabase.from('user_roles').select('role_name').eq('user_id', accessor_user_id);\n                const { data: targetRoles } = await supabase.from('user_roles').select('role_name').eq('user_id', target_user_id);\n                const accessorIsSecretary = accessorRoles?.some((r)=>r.role_name === 'secretary');\n                const targetIsDoctor = targetRoles?.some((r)=>r.role_name === 'doctor');\n                if (!accessorIsSecretary || !targetIsDoctor) {\n                    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(null, 'For secretary_to_doctor association, accessor must be secretary and target must be doctor', 400);\n                }\n            }\n            if (association_type === 'doctor_to_doctor') {\n                // Check if both are doctors\n                const { data: accessorRoles } = await supabase.from('user_roles').select('role_name').eq('user_id', accessor_user_id);\n                const { data: targetRoles } = await supabase.from('user_roles').select('role_name').eq('user_id', target_user_id);\n                const accessorIsDoctor = accessorRoles?.some((r)=>r.role_name === 'doctor');\n                const targetIsDoctor = targetRoles?.some((r)=>r.role_name === 'doctor');\n                if (!accessorIsDoctor || !targetIsDoctor) {\n                    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(null, 'For doctor_to_doctor association, both users must be doctors', 400);\n                }\n            }\n            // Create the association\n            const { data: association, error } = await supabase.from('user_associations').insert({\n                accessor_user_id,\n                target_user_id,\n                association_type,\n                permissions: permissions || null,\n                created_by: userId,\n                is_active: true\n            }).select(`\n          *,\n          accessor_user:users!accessor_user_id(id, name, email, role),\n          target_user:users!target_user_id(id, name, email, role),\n          created_by_user:users!created_by(id, name, email)\n        `).single();\n            if (error) {\n                if (error.code === '23505') {\n                    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(null, 'Association already exists', 409);\n                }\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n            }\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(association, 'Association created successfully', 201);\n        } catch (error) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n        }\n    });\n}\nasync function PATCH(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withAuth)(request, async (userId, supabase)=>{\n        try {\n            const { searchParams } = new URL(request.url);\n            const associationId = searchParams.get('id');\n            if (!associationId) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(null, 'Association ID is required', 400);\n            }\n            const body = await request.json();\n            // Check if user can update this association\n            const { data: association, error: fetchError } = await supabase.from('user_associations').select('*').eq('id', associationId).single();\n            if (fetchError) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(fetchError);\n            }\n            if (!association) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(null, 'Association not found', 404);\n            }\n            // Check permissions\n            const { data: userRoles, error: rolesError } = await supabase.from('user_roles').select('role_name').eq('user_id', userId);\n            if (rolesError) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(rolesError);\n            }\n            const isAdmin = userRoles?.some((role)=>role.role_name === 'admin');\n            const canUpdate = isAdmin || association.created_by === userId;\n            if (!canUpdate) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(null, 'You can only update associations you created', 403);\n            }\n            // Update the association\n            const { data: updatedAssociation, error } = await supabase.from('user_associations').update(body).eq('id', associationId).select(`\n          *,\n          accessor_user:users!accessor_user_id(id, name, email, role),\n          target_user:users!target_user_id(id, name, email, role),\n          created_by_user:users!created_by(id, name, email)\n        `).single();\n            if (error) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n            }\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(updatedAssociation, 'Association updated successfully');\n        } catch (error) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n        }\n    });\n}\nasync function DELETE(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withAuth)(request, async (userId, supabase)=>{\n        try {\n            const { searchParams } = new URL(request.url);\n            const associationId = searchParams.get('id');\n            if (!associationId) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(null, 'Association ID is required', 400);\n            }\n            // Check if user can delete this association\n            const { data: association, error: fetchError } = await supabase.from('user_associations').select('*').eq('id', associationId).single();\n            if (fetchError) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(fetchError);\n            }\n            if (!association) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(null, 'Association not found', 404);\n            }\n            // Check permissions\n            const { data: userRoles, error: rolesError } = await supabase.from('user_roles').select('role_name').eq('user_id', userId);\n            if (rolesError) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(rolesError);\n            }\n            const isAdmin = userRoles?.some((role)=>role.role_name === 'admin');\n            const canDelete = isAdmin || association.created_by === userId;\n            if (!canDelete) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(null, 'You can only delete associations you created', 403);\n            }\n            // Delete the association\n            const { error } = await supabase.from('user_associations').delete().eq('id', associationId);\n            if (error) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n            }\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(null, 'Association deleted successfully');\n        } catch (error) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/user-associations/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api-utils.ts":
/*!******************************!*\
  !*** ./src/lib/api-utils.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApiResponse: () => (/* binding */ createApiResponse),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   withAuth: () => (/* binding */ withAuth),\n/* harmony export */   withAuthAndPermission: () => (/* binding */ withAuthAndPermission)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_permissions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/permissions */ \"(rsc)/./src/lib/permissions.ts\");\n\n\n\nfunction createApiResponse(data, message, status = 200) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        data,\n        message\n    }, {\n        status\n    });\n}\nasync function withAuth(request, handler) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        return await handler(user.id, supabase);\n    } catch (error) {\n        console.error('API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function withAuthAndPermission(request, resource, action, handler) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return createApiResponse(undefined, 'Unauthorized', 401);\n        }\n        // Check permissions\n        const hasAccess = await (0,_lib_permissions__WEBPACK_IMPORTED_MODULE_2__.hasPermission)(user.id, resource, action);\n        if (!hasAccess) {\n            return createApiResponse(undefined, 'Forbidden: Insufficient permissions', 403);\n        }\n        return await handler(user.id, supabase);\n    } catch (error) {\n        console.error('API Error:', error);\n        return createApiResponse(undefined, error instanceof Error ? error.message : 'Internal server error', 500);\n    }\n}\nfunction handleApiError(error) {\n    console.error('API Error:', error);\n    if (error?.code === 'PGRST116') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Resource not found'\n        }, {\n            status: 404\n        });\n    }\n    if (error?.code === '23505') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Resource already exists'\n        }, {\n            status: 409\n        });\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: error?.message || 'Internal server error'\n    }, {\n        status: 500\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/permissions.ts":
/*!********************************!*\
  !*** ./src/lib/permissions.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_PERMISSIONS: () => (/* binding */ DEFAULT_PERMISSIONS),\n/* harmony export */   getUserPermissions: () => (/* binding */ getUserPermissions),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hasRole: () => (/* binding */ hasRole),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   requirePermission: () => (/* binding */ requirePermission),\n/* harmony export */   requireRole: () => (/* binding */ requireRole)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://zmwdnemlzndjavlriyrc.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Create a Supabase client with service role key for server-side operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n/**\n * Check if a user has a specific permission\n */ async function hasPermission(userId, resource, action) {\n    try {\n        // Get user roles\n        const { data: userRoles, error: rolesError } = await supabaseAdmin.from('user_roles').select('role_name').eq('user_id', userId);\n        if (rolesError || !userRoles || userRoles.length === 0) {\n            return false;\n        }\n        // Check if any of the user's roles have the required permission\n        const roleNames = userRoles.map((role)=>role.role_name);\n        const { data: permissions, error: permissionsError } = await supabaseAdmin.from('permissions').select('*').in('role_name', roleNames).eq('resource', resource).eq('action', action);\n        if (permissionsError) {\n            console.error('Error checking permissions:', permissionsError);\n            return false;\n        }\n        return permissions && permissions.length > 0;\n    } catch (error) {\n        console.error('Error in hasPermission:', error);\n        return false;\n    }\n}\n/**\n * Check if a user has any of the specified roles\n */ async function hasRole(userId, roles) {\n    try {\n        const { data: userRoles, error } = await supabaseAdmin.from('user_roles').select('role_name').eq('user_id', userId).in('role_name', roles);\n        if (error) {\n            console.error('Error checking roles:', error);\n            return false;\n        }\n        return userRoles && userRoles.length > 0;\n    } catch (error) {\n        console.error('Error in hasRole:', error);\n        return false;\n    }\n}\n/**\n * Get all permissions for a user\n */ async function getUserPermissions(userId) {\n    try {\n        // Get user roles\n        const { data: userRoles, error: rolesError } = await supabaseAdmin.from('user_roles').select('role_name').eq('user_id', userId);\n        if (rolesError || !userRoles || userRoles.length === 0) {\n            return [];\n        }\n        // Get all permissions for these roles\n        const roleNames = userRoles.map((role)=>role.role_name);\n        const { data: permissions, error: permissionsError } = await supabaseAdmin.from('permissions').select('resource, action').in('role_name', roleNames);\n        if (permissionsError) {\n            console.error('Error getting user permissions:', permissionsError);\n            return [];\n        }\n        return permissions || [];\n    } catch (error) {\n        console.error('Error in getUserPermissions:', error);\n        return [];\n    }\n}\n/**\n * Check if a user is an admin\n */ async function isAdmin(userId) {\n    return hasRole(userId, [\n        'admin'\n    ]);\n}\n/**\n * Middleware function to check permissions for API routes\n */ function requirePermission(resource, action) {\n    return async (userId)=>{\n        return hasPermission(userId, resource, action);\n    };\n}\n/**\n * Middleware function to check roles for API routes\n */ function requireRole(roles) {\n    return async (userId)=>{\n        return hasRole(userId, roles);\n    };\n}\n/**\n * Default permissions for each role\n */ const DEFAULT_PERMISSIONS = {\n    admin: [\n        // Full access to everything\n        {\n            resource: 'appointments',\n            action: 'create'\n        },\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'appointments',\n            action: 'update'\n        },\n        {\n            resource: 'appointments',\n            action: 'delete'\n        },\n        {\n            resource: 'patients',\n            action: 'create'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'update'\n        },\n        {\n            resource: 'patients',\n            action: 'delete'\n        },\n        {\n            resource: 'medical_records',\n            action: 'create'\n        },\n        {\n            resource: 'medical_records',\n            action: 'read'\n        },\n        {\n            resource: 'medical_records',\n            action: 'update'\n        },\n        {\n            resource: 'medical_records',\n            action: 'delete'\n        },\n        {\n            resource: 'procedures',\n            action: 'create'\n        },\n        {\n            resource: 'procedures',\n            action: 'read'\n        },\n        {\n            resource: 'procedures',\n            action: 'update'\n        },\n        {\n            resource: 'procedures',\n            action: 'delete'\n        },\n        {\n            resource: 'settings',\n            action: 'read'\n        },\n        {\n            resource: 'settings',\n            action: 'update'\n        }\n    ],\n    doctor: [\n        // Can manage appointments and patients, read procedures\n        {\n            resource: 'appointments',\n            action: 'create'\n        },\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'appointments',\n            action: 'update'\n        },\n        {\n            resource: 'patients',\n            action: 'create'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'update'\n        },\n        {\n            resource: 'medical_records',\n            action: 'create'\n        },\n        {\n            resource: 'medical_records',\n            action: 'read'\n        },\n        {\n            resource: 'medical_records',\n            action: 'update'\n        },\n        {\n            resource: 'procedures',\n            action: 'read'\n        }\n    ],\n    secretary: [\n        // Can manage appointments and patients, read procedures\n        {\n            resource: 'appointments',\n            action: 'create'\n        },\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'appointments',\n            action: 'update'\n        },\n        {\n            resource: 'patients',\n            action: 'create'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'update'\n        },\n        {\n            resource: 'procedures',\n            action: 'read'\n        }\n    ],\n    assistant: [\n        // Read-only access to appointments and patients\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/permissions.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://zmwdnemlzndjavlriyrc.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inptd2RuZW1sem5kamF2bHJpeXJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MTE5NTksImV4cCI6MjA2NzM4Nzk1OX0.XNRQjZmMZ7s4aKrJVSQFlu9ASryGJc5fBX6iNnjOPEM\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-associations%2Froute&page=%2Fapi%2Fuser-associations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-associations%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();