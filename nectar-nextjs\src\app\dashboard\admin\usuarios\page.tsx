'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'
import { makeAuthenticatedRequest } from '@/lib/api-client'
import { <PERSON>, UserPlus, Shield, <PERSON><PERSON><PERSON>, Trash2, <PERSON>, Eye, <PERSON> } from 'lucide-react'
import { UserAssociationsManager } from '@/components/UserAssociationsManager'

interface User {
  id: string
  email: string
  name: string | null
  phone: string | null
  role: string
  is_active: boolean
  created_at: string
  updated_at: string
}

interface UserRole {
  id: string
  user_id: string
  role_name: string
  created_at: string
}

interface Permission {
  id: string
  role_name: string
  resource: string
  action: string
}

export default function UsersAdminPage() {
  const [users, setUsers] = useState<User[]>([])
  const [userRoles, setUserRoles] = useState<UserRole[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('users')
  const { toast } = useToast()

  // Form states
  const [userForm, setUserForm] = useState({
    email: '',
    name: '',
    phone: '',
    password: '',
    role: 'secretary',
    is_active: true
  })

  const [roleForm, setRoleForm] = useState({
    user_id: '',
    role_name: ''
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      await Promise.all([
        fetchUsers(),
        fetchUserRoles(),
        fetchPermissions()
      ])
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: "Erro ao carregar dados",
        description: "Não foi possível carregar os dados dos usuários.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchUsers = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/admin/users')
      if (response.ok) {
        const result = await response.json()
        setUsers(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching users:', error)
    }
  }

  const fetchUserRoles = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/user-roles')
      if (response.ok) {
        const result = await response.json()
        setUserRoles(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching user roles:', error)
    }
  }

  const fetchPermissions = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/permissions')
      if (response.ok) {
        const result = await response.json()
        setPermissions(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching permissions:', error)
    }
  }

  const handleCreateUser = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/admin/users', {
        method: 'POST',
        body: JSON.stringify(userForm)
      })

      if (response.ok) {
        toast({
          title: "Usuário criado",
          description: "Usuário criado com sucesso.",
        })
        setIsDialogOpen(false)
        setUserForm({
          email: '',
          name: '',
          phone: '',
          password: '',
          role: 'secretary',
          is_active: true
        })
        fetchUsers()
      } else {
        throw new Error('Failed to create user')
      }
    } catch (error) {
      console.error('Error creating user:', error)
      toast({
        title: "Erro ao criar usuário",
        description: "Não foi possível criar o usuário.",
        variant: "destructive",
      })
    }
  }

  const handleAssignRole = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/user-roles', {
        method: 'POST',
        body: JSON.stringify(roleForm)
      })

      if (response.ok) {
        toast({
          title: "Role atribuído",
          description: "Role atribuído com sucesso ao usuário.",
        })
        setRoleForm({ user_id: '', role_name: '' })
        fetchUserRoles()
      } else {
        throw new Error('Failed to assign role')
      }
    } catch (error) {
      console.error('Error assigning role:', error)
      toast({
        title: "Erro ao atribuir role",
        description: "Não foi possível atribuir o role ao usuário.",
        variant: "destructive",
      })
    }
  }

  const handleToggleUserStatus = async (userId: string, isActive: boolean) => {
    try {
      const response = await makeAuthenticatedRequest(`/api/admin/users/${userId}`, {
        method: 'PATCH',
        body: JSON.stringify({ is_active: !isActive })
      })

      if (response.ok) {
        toast({
          title: "Status atualizado",
          description: `Usuário ${!isActive ? 'ativado' : 'desativado'} com sucesso.`,
        })
        fetchUsers()
      } else {
        throw new Error('Failed to update user status')
      }
    } catch (error) {
      console.error('Error updating user status:', error)
      toast({
        title: "Erro ao atualizar status",
        description: "Não foi possível atualizar o status do usuário.",
        variant: "destructive",
      })
    }
  }

  const getUserRoles = (userId: string) => {
    return userRoles.filter(ur => ur.user_id === userId).map(ur => ur.role_name)
  }

  const getRolePermissions = (roleName: string) => {
    return permissions.filter(p => p.role_name === roleName)
  }

  const availableRoles = ['admin', 'doctor', 'secretary']

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Carregando...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Administração de Usuários</h1>
          <p className="text-muted-foreground">
            Gerencie usuários, roles e permissões do sistema
          </p>
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <UserPlus className="mr-2 h-4 w-4" />
              Novo Usuário
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Criar Novo Usuário</DialogTitle>
              <DialogDescription>
                Adicione um novo usuário ao sistema
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="email">E-mail *</Label>
                <Input
                  id="email"
                  type="email"
                  value={userForm.email}
                  onChange={(e) => setUserForm({ ...userForm, email: e.target.value })}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="name">Nome</Label>
                <Input
                  id="name"
                  value={userForm.name}
                  onChange={(e) => setUserForm({ ...userForm, name: e.target.value })}
                  placeholder="Nome completo"
                />
              </div>
              <div>
                <Label htmlFor="phone">Telefone</Label>
                <Input
                  id="phone"
                  value={userForm.phone}
                  onChange={(e) => setUserForm({ ...userForm, phone: e.target.value })}
                  placeholder="(11) 99999-9999"
                />
              </div>
              <div>
                <Label htmlFor="password">Senha Inicial *</Label>
                <Input
                  id="password"
                  type="password"
                  value={userForm.password}
                  onChange={(e) => setUserForm({ ...userForm, password: e.target.value })}
                  placeholder="Senha temporária para primeiro acesso"
                />
              </div>
              <div>
                <Label htmlFor="role">Role Inicial</Label>
                <Select value={userForm.role} onValueChange={(value) => setUserForm({ ...userForm, role: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {availableRoles.map(role => (
                      <SelectItem key={role} value={role}>
                        {role.charAt(0).toUpperCase() + role.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancelar
              </Button>
              <Button onClick={handleCreateUser}>
                Criar Usuário
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Usuários
          </TabsTrigger>
          <TabsTrigger value="associations" className="flex items-center gap-2">
            <Link className="h-4 w-4" />
            Associações
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Roles
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Permissões
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Usuários do Sistema</CardTitle>
              <CardDescription>
                Lista de todos os usuários cadastrados no sistema
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome</TableHead>
                    <TableHead>E-mail</TableHead>
                    <TableHead>Telefone</TableHead>
                    <TableHead>Roles</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">
                        {user.name || 'Sem nome'}
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{user.phone || '-'}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {getUserRoles(user.id).map((role) => (
                            <Badge key={role} variant="secondary">
                              {role}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={user.is_active ? "default" : "destructive"}>
                          {user.is_active ? 'Ativo' : 'Inativo'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedUser(user)}
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleToggleUserStatus(user.id, user.is_active)}
                          >
                            {user.is_active ? 'Desativar' : 'Ativar'}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="associations" className="space-y-4">
          <UserAssociationsManager />
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Atribuição de Roles</CardTitle>
              <CardDescription>
                Atribua roles aos usuários do sistema
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="user_select">Usuário</Label>
                  <Select value={roleForm.user_id} onValueChange={(value) => setRoleForm({ ...roleForm, user_id: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione um usuário" />
                    </SelectTrigger>
                    <SelectContent>
                      {users.map(user => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.name || user.email}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="role_select">Role</Label>
                  <Select value={roleForm.role_name} onValueChange={(value) => setRoleForm({ ...roleForm, role_name: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione um role" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableRoles.map(role => (
                        <SelectItem key={role} value={role}>
                          {role.charAt(0).toUpperCase() + role.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <Button onClick={handleAssignRole} disabled={!roleForm.user_id || !roleForm.role_name}>
                Atribuir Role
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Roles Atribuídos</CardTitle>
              <CardDescription>
                Lista de todos os roles atribuídos aos usuários
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Usuário</TableHead>
                    <TableHead>E-mail</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Data de Atribuição</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {userRoles.map((userRole) => {
                    const user = users.find(u => u.id === userRole.user_id)
                    return (
                      <TableRow key={userRole.id}>
                        <TableCell>{user?.name || 'Usuário não encontrado'}</TableCell>
                        <TableCell>{user?.email || '-'}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{userRole.role_name}</Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(userRole.created_at).toLocaleDateString('pt-BR')}
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Matriz de Permissões</CardTitle>
              <CardDescription>
                Visualize as permissões de cada role no sistema
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {availableRoles.map(role => (
                  <div key={role} className="space-y-2">
                    <h3 className="text-lg font-semibold capitalize">{role}</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                      {getRolePermissions(role).map(permission => (
                        <Badge key={`${permission.resource}-${permission.action}`} variant="outline">
                          {permission.resource}:{permission.action}
                        </Badge>
                      ))}
                    </div>
                    {getRolePermissions(role).length === 0 && (
                      <p className="text-sm text-muted-foreground">Nenhuma permissão atribuída</p>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
