import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // For now, return default settings since we don't have a settings table yet
      const defaultSettings = {
        profile: {
          name: '',
          email: '',
          phone: '',
          specialty: '',
          crm: ''
        },
        notifications: {
          email_appointments: true,
          sms_reminders: false,
          push_notifications: true,
          marketing_emails: false
        },
        integrations: {
          whatsapp_token: '',
          whatsapp_phone: '',
          email_smtp_host: '',
          email_smtp_port: '',
          email_smtp_user: '',
          email_smtp_password: ''
        }
      }

      return createApiResponse(defaultSettings)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
