# Nectar - Análise Arquitetural e Mapeamento do Projeto

## 📋 Visão Geral do Projeto

**Nectar** é um sistema completo de gestão para clínicas médicas desenvolvido com Next.js 14, TypeScript e Supabase. O sistema segue uma arquitetura moderna com separação clara de responsabilidades e foco em segurança e experiência do usuário.

## 🏗️ Arquitetura Atual

### Frontend Architecture
- **Framework**: Next.js 14 com App Router
- **Linguagem**: TypeScript para tipagem estática
- **Estilização**: Tailwind CSS + Shadcn/ui components
- **Estado**: React hooks + Context API para autenticação e permissões
- **Formulários**: React Hook Form + Zod validation
- **Calendário**: FullCalendar para visualização de agenda

### Backend Architecture
- **BaaS**: Supabase (PostgreSQL + Auth + Storage + RLS)
- **API Routes**: Next.js API routes para lógica de negócio
- **Autenticação**: Supabase Auth com middleware de proteção
- **Segurança**: Row Level Security (RLS) para isolamento de dados

### Database Schema
```
users (extends auth.users)
├── patients (1:N)
├── healthcare_professionals (1:N)
├── procedures (1:N)
├── appointments (1:N)
│   └── appointment_procedures (N:M with procedures)
├── medical_records (1:N)
├── patient_attachments (1:N)
└── clinic_settings (1:1)
```

## 📁 Estrutura de Diretórios

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   ├── auth/              # Autenticação
│   ├── dashboard/         # Área logada
│   └── globals.css        # Estilos globais
├── components/            # Componentes React
│   └── ui/               # Shadcn components
├── hooks/                # Custom hooks
├── lib/                  # Utilitários e configurações
└── types/                # Definições TypeScript
```

## 🔄 Padrões de Desenvolvimento Identificados

### ✅ Padrões Consistentes

1. **API Routes Pattern**
   - Uso consistente de `withAuth` wrapper
   - Padronização de responses com `createApiResponse`
   - Error handling centralizado com `handleApiError`

2. **Component Organization**
   - Separação clara entre UI components e business logic
   - Uso consistente de TypeScript interfaces
   - Shadcn/ui para componentes base

3. **State Management**
   - Context providers para estado global (Auth, Permissions)
   - React Query para cache de dados
   - Local state com useState para componentes

4. **Security Patterns**
   - RLS policies em todas as tabelas
   - Middleware de autenticação
   - Validação com Zod schemas

### ⚠️ Inconsistências Identificadas

1. **Loading States**
   - Alguns componentes usam loading spinners simples
   - Outros não têm loading states
   - Falta de skeleton components padronizados

2. **Error Handling**
   - Mensagens de erro genéricas em alguns lugares
   - Falta de tratamento específico para diferentes tipos de erro

3. **Mobile Responsiveness**
   - Implementação inconsistente de breakpoints
   - Alguns componentes não adaptam bem para mobile

4. **Status Colors**
   - Cores de status diferentes entre componentes
   - Falta de sistema de cores centralizado

## 🎨 Sistema de Design

### Cores Principais
```css
--primary: 207 73% 45%        /* Medical blue */
--secondary: 160 35% 90%      /* Clean green */
--destructive: 0 70% 55%      /* Error red */
--muted: 215 20% 95%          /* Neutral gray */
```

### Breakpoints
```css
mobile: max-width 767px
tablet: 768px - 1023px
desktop: min-width 1024px
```

## 📊 Componentes Principais

### 1. Dashboard
- **Localização**: `src/app/dashboard/page.tsx`
- **Funcionalidade**: Overview com estatísticas e próximas consultas
- **Estado**: Usa hooks customizados para data fetching

### 2. Agenda/Calendar
- **Localização**: `src/app/dashboard/agenda/page.tsx`
- **Funcionalidade**: Visualização dupla (cards + FullCalendar)
- **Componentes**: `FullCalendarView.tsx`, `AppointmentForm.tsx`

### 3. Pacientes
- **Localização**: `src/app/dashboard/pacientes/`
- **Funcionalidade**: CRUD de pacientes com detalhes e anexos
- **Padrão**: Lista → Detalhes → Tabs (Dados, Histórico, Anexos)

### 4. Prontuário Médico
- **Localização**: `src/app/dashboard/prontuario/`
- **Funcionalidade**: Registro de consultas com auto-save
- **Segurança**: Criptografia de dados sensíveis

## 🔧 Hooks Customizados

### Autenticação e Permissões
- `useAuth`: Gerenciamento de sessão
- `usePermissions`: Controle de acesso baseado em roles

### Data Fetching
- `useApiRequest`: Request genérico com retry e error handling
- `useAsyncOperation`: Operações assíncronas com loading states
- `useDataFetching`: Especializado para busca de dados

### Utilitários
- `useIsMobile`: Detecção de dispositivo móvel
- `useToast`: Notificações padronizadas

## 🚀 Funcionalidades Principais

1. **Gestão de Pacientes**: CRUD completo com anexos
2. **Agenda Avançada**: Calendário duplo com drag-and-drop
3. **Prontuário Eletrônico**: Com criptografia e auto-save
4. **Sistema de Permissões**: Baseado em roles
5. **Anexos de Arquivos**: Integração com Supabase Storage

## 🔍 Áreas de Melhoria Identificadas

### 1. UX/UI Consistency
- [ ] Padronizar loading states com skeleton components
- [ ] Unificar sistema de cores para status
- [ ] Melhorar responsividade mobile

### 2. Performance
- [ ] Implementar lazy loading para componentes pesados
- [ ] Otimizar queries do banco de dados
- [ ] Adicionar cache strategies

### 3. Error Handling
- [ ] Mensagens de erro mais específicas
- [ ] Fallback components para erros
- [ ] Logging estruturado

### 4. Accessibility
- [ ] Melhorar navegação por teclado
- [ ] Adicionar ARIA labels
- [ ] Contraste de cores

## 📈 Métricas de Qualidade

### Code Quality
- ✅ TypeScript coverage: ~95%
- ✅ Component reusability: Alta
- ✅ API consistency: Boa
- ⚠️ Test coverage: Não implementado

### Performance
- ✅ Bundle size: Otimizado
- ✅ Loading times: Aceitável
- ⚠️ Mobile performance: Pode melhorar

### Security
- ✅ RLS implementation: Completa
- ✅ Authentication: Robusta
- ✅ Data encryption: Implementada para prontuários

## 🎯 Próximos Passos

1. **Imediato**: Corrigir bugs críticos (procedures error, form data)
2. **Curto prazo**: Implementar melhorias de UX (loading, mobile)
3. **Médio prazo**: Sistema de busca inteligente
4. **Longo prazo**: Anexos em consultas, relatórios avançados

---

*Documento gerado em: 2025-01-12*
*Versão: 1.0*
