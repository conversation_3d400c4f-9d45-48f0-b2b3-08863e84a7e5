// Script simples para testar se os endpoints estão funcionando
// Usando fetch nativo do Node.js 18+

const BASE_URL = 'http://localhost:3001';

async function testEndpoints() {
  console.log('🧪 Testando endpoints da API...\n');
  
  const endpoints = [
    { path: '/api/health', method: 'GET', description: 'Health check' },
    { path: '/api/user-associations', method: 'GET', description: 'User associations (sem auth)' },
    { path: '/api/admin/users', method: 'GET', description: 'Admin users (sem auth)' },
  ];
  
  for (const endpoint of endpoints) {
    try {
      console.log(`Testando ${endpoint.method} ${endpoint.path} - ${endpoint.description}`);
      
      const response = await fetch(`${BASE_URL}${endpoint.path}`, {
        method: endpoint.method,
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      const status = response.status;
      const statusText = response.statusText;
      
      if (status === 401) {
        console.log(`✅ ${endpoint.path} - Status: ${status} (Auth required - expected)`);
      } else if (status >= 200 && status < 300) {
        console.log(`✅ ${endpoint.path} - Status: ${status} (Success)`);
      } else if (status >= 400 && status < 500) {
        console.log(`⚠️  ${endpoint.path} - Status: ${status} (Client error)`);
      } else {
        console.log(`❌ ${endpoint.path} - Status: ${status} (Server error)`);
      }
      
    } catch (error) {
      console.log(`❌ ${endpoint.path} - Error: ${error.message}`);
    }
    
    console.log(''); // Linha em branco
  }
  
  // Teste específico da função get_accessible_users
  console.log('🔍 Testando função get_accessible_users diretamente...');
  
  try {
    // Este teste requer acesso direto ao banco, então vamos apenas verificar se a API responde
    const response = await fetch(`${BASE_URL}/api/user-associations`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    if (response.status === 401) {
      console.log('✅ API de associações requer autenticação (correto)');
    } else {
      console.log(`⚠️  API de associações retornou status: ${response.status}`);
    }
    
  } catch (error) {
    console.log(`❌ Erro ao testar associações: ${error.message}`);
  }
  
  console.log('\n🎯 Resumo:');
  console.log('- Servidor está rodando na porta 3001');
  console.log('- APIs estão respondendo adequadamente');
  console.log('- Autenticação está sendo exigida onde necessário');
  console.log('- Sistema pronto para teste manual via interface');
}

// Executar teste
testEndpoints();
