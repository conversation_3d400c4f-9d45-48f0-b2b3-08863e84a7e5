"use client"

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import {
  LayoutDashboard,
  Calendar,
  Users,
  Settings,
  Heart,
  Shield
} from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";

const sidebarItems = [
  { title: "Dashboard", url: "/dashboard", icon: LayoutDashboard },
  { title: "Agenda", url: "/dashboard/agenda", icon: Calendar },
  { title: "Pacientes", url: "/dashboard/pacientes", icon: Users },
  { title: "Configurações", url: "/dashboard/configuracoes", icon: Settings },
];

const adminItems = [
  { title: "Usuários", url: "/dashboard/admin/usuarios", icon: Shield },
];

export function AppSidebar() {
  const { state } = useSidebar();
  const pathname = usePathname();
  const { isAdmin, loading, userRoles } = usePermissions();

  // Debug log
  const isAdminUser = isAdmin ? isAdmin() : false;
  console.log('🔍 Sidebar permissions:', { isAdmin: isAdminUser, loading, userRoles });

  const isActive = (path: string) => pathname === path;

  return (
    <Sidebar
      collapsible="icon"
    >
      <SidebarContent>
        {/* Logo */}
        <div className="p-6 border-b">
          <div className="flex items-center">
            <Heart className="h-8 w-8 text-primary mr-2" />
            {state === "expanded" && <span className="text-xl font-bold text-foreground">Nectar Saúde</span>}
          </div>
        </div>

        <SidebarGroup>
          <SidebarGroupLabel>Menu Principal</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {sidebarItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <Link
                      href={item.url}
                      className={isActive(item.url)
                        ? "bg-primary/10 text-primary font-medium border-r-2 border-primary"
                        : "hover:bg-muted/50"
                      }
                    >
                      <item.icon className="mr-3 h-5 w-5" />
                      {state === "expanded" && <span>{item.title}</span>}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Admin Section - Only show for admin users */}
        {isAdminUser && !loading && (
          <SidebarGroup>
            <SidebarGroupLabel>Administração</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {adminItems.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild>
                      <Link
                        href={item.url}
                        className={isActive(item.url)
                          ? "bg-primary/10 text-primary font-medium border-r-2 border-primary"
                          : "hover:bg-muted/50"
                        }
                      >
                        <item.icon className="mr-3 h-5 w-5" />
                        {state === "expanded" && <span>{item.title}</span>}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}
      </SidebarContent>
    </Sidebar>
  );
}
