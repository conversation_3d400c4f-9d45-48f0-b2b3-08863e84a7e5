"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/pacientes/[id]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/pacientes/[id]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/pacientes/[id]/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/date-utils */ \"(app-pages-browser)/./src/lib/date-utils.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PatientDetailPage = ()=>{\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    const patientId = params.id;\n    const [patient, setPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editMode, setEditMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadDialogOpen, setUploadDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [medicalRecordOpen, setMedicalRecordOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAppointment, setSelectedAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [medicalRecords, setMedicalRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newRecord, setNewRecord] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [editForm, setEditForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        phone: '',\n        birth_date: '',\n        cpf: '',\n        address: '',\n        notes: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PatientDetailPage.useEffect\": ()=>{\n            if (patientId) {\n                fetchPatientData();\n                fetchAppointments();\n                fetchAttachments();\n            }\n        }\n    }[\"PatientDetailPage.useEffect\"], [\n        patientId\n    ]);\n    const fetchPatientData = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patients/\".concat(patientId));\n            if (!response.ok) throw new Error('Failed to fetch patient');\n            const result = await response.json();\n            const data = result.data || result;\n            setPatient(data);\n            setEditForm({\n                name: data.name || '',\n                email: data.email || '',\n                phone: data.phone || '',\n                birth_date: data.birth_date || '',\n                cpf: data.cpf || '',\n                address: data.address || '',\n                notes: data.notes || ''\n            });\n        } catch (error) {\n            console.error('Error fetching patient:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao carregar dados do paciente.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchAppointments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/appointments?patient_id=\".concat(patientId));\n            if (!response.ok) throw new Error('Failed to fetch appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching appointments:', error);\n            setAppointments([]);\n        }\n    };\n    const fetchAttachments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patient-attachments?patient_id=\".concat(patientId));\n            if (!response.ok) throw new Error('Failed to fetch attachments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAttachments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching attachments:', error);\n            setAttachments([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSavePatient = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patients/\".concat(patientId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(editForm)\n            });\n            if (!response.ok) throw new Error('Failed to update patient');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Dados do paciente atualizados.\"\n            });\n            setEditMode(false);\n            fetchPatientData();\n        } catch (error) {\n            console.error('Error updating patient:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar paciente.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleFileUpload = async ()=>{\n        if (!selectedFile) return;\n        try {\n            const formData = new FormData();\n            formData.append('file', selectedFile);\n            formData.append('patient_id', patientId);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)('/api/patient-attachments', {\n                method: 'POST',\n                body: formData\n            });\n            if (!response.ok) throw new Error('Failed to upload file');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Arquivo enviado com sucesso.\"\n            });\n            setUploadDialogOpen(false);\n            setSelectedFile(null);\n            fetchAttachments();\n        } catch (error) {\n            console.error('Error uploading file:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao enviar arquivo.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteAttachment = async (attachmentId)=>{\n        if (!confirm('Tem certeza que deseja excluir este arquivo?')) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patient-attachments/\".concat(attachmentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete attachment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Arquivo excluído com sucesso.\"\n            });\n            fetchAttachments();\n        } catch (error) {\n            console.error('Error deleting attachment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao excluir arquivo.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const updateAppointmentStatus = async (appointmentId, status)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Status da consulta atualizado.\"\n            });\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error updating appointment status:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar status da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDownloadAttachment = async (attachmentId, fileName)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patient-attachments/\".concat(attachmentId));\n            if (!response.ok) throw new Error('Failed to get download URL');\n            const result = await response.json();\n            const data = result.data || result;\n            // Create a temporary link to download the file\n            const link = document.createElement('a');\n            link.href = data.download_url;\n            link.download = fileName;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        } catch (error) {\n            console.error('Error downloading file:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao baixar arquivo.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (!bytes) return 'Tamanho desconhecido';\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(1024));\n        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n    };\n    const calculateAge = (birthDate)=>{\n        if (!birthDate) return 'Idade não informada';\n        const today = new Date();\n        const birth = new Date(birthDate);\n        const age = today.getFullYear() - birth.getFullYear();\n        const monthDiff = today.getMonth() - birth.getMonth();\n        if (monthDiff < 0 || monthDiff === 0 && today.getDate() < birth.getDate()) {\n            return \"\".concat(age - 1, \" anos\");\n        }\n        return \"\".concat(age, \" anos\");\n    };\n    const handleOpenMedicalRecord = async (appointment)=>{\n        setSelectedAppointment(appointment);\n        setMedicalRecordOpen(true);\n        await fetchMedicalRecords(appointment.id);\n    };\n    const fetchMedicalRecords = async (appointmentId)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/medical-records?appointment_id=\".concat(appointmentId));\n            if (!response.ok) throw new Error('Failed to fetch medical records');\n            const result = await response.json();\n            const data = result.data || result;\n            setMedicalRecords(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching medical records:', error);\n            setMedicalRecords([]);\n        }\n    };\n    const handleAddMedicalRecord = async ()=>{\n        if (!newRecord.trim() || !selectedAppointment) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)('/api/medical-records', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    appointment_id: selectedAppointment.id,\n                    patient_id: patientId,\n                    notes: newRecord.trim()\n                })\n            });\n            if (!response.ok) throw new Error('Failed to add medical record');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Registro médico adicionado.\"\n            });\n            setNewRecord('');\n            await fetchMedicalRecords(selectedAppointment.id);\n        } catch (error) {\n            console.error('Error adding medical record:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao adicionar registro médico.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleStartTreatment = async ()=>{\n        if (!selectedAppointment) return;\n        try {\n            await updateAppointmentStatus(selectedAppointment.id, 'in_progress');\n            toast({\n                title: \"Atendimento iniciado\",\n                description: \"O status da consulta foi atualizado para 'Em Andamento'.\"\n            });\n            // Refresh appointments to update the status\n            await fetchAppointments();\n            // Update the selected appointment status\n            setSelectedAppointment({\n                ...selectedAppointment,\n                status: 'in_progress'\n            });\n        } catch (error) {\n            console.error('Error starting treatment:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-[250px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 w-[200px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 w-[80px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 w-[80px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                    lineNumber: 399,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-4 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 w-[120px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 w-[150px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 w-[100px] bg-muted animate-pulse rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: Array.from({\n                                            length: 4\n                                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 w-[80px] bg-muted animate-pulse rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-10 w-full bg-muted animate-pulse rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: Array.from({\n                                            length: 4\n                                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 w-[80px] bg-muted animate-pulse rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-10 w-full bg-muted animate-pulse rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n            lineNumber: 397,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!patient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"Paciente n\\xe3o encontrado.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    onClick: ()=>router.back(),\n                    className: \"mt-4\",\n                    children: \"Voltar\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                    lineNumber: 448,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n            lineNumber: 446,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: patient.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: [\n                                    \"Paciente desde \",\n                                    (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateBR)(patient.created_at)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleSavePatient,\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Salvar\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>setEditMode(false),\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Cancelar\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setEditMode(true),\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Editar\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>router.back(),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: \"Voltar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                lineNumber: 457,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                defaultValue: \"dados\",\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                        className: \"grid w-full grid-cols-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"dados\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Dados Principais\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"consultas\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Hist\\xf3rico de Consultas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"anexos\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Anexos\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"dados\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"Informa\\xe7\\xf5es Pessoais\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Dados b\\xe1sicos do paciente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"name\",\n                                                        children: \"Nome Completo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"name\",\n                                                        value: editForm.name,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                name: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: patient.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"birth_date\",\n                                                        children: \"Data de Nascimento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"birth_date\",\n                                                        type: \"date\",\n                                                        value: editForm.birth_date,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                birth_date: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: patient.birth_date ? \"\".concat((0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateBR)(patient.birth_date), \" (\").concat(calculateAge(patient.birth_date), \")\") : 'Não informado'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"cpf\",\n                                                        children: \"CPF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"cpf\",\n                                                        value: editForm.cpf,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                cpf: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: patient.cpf || 'Não informado'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"phone\",\n                                                        children: \"Telefone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"phone\",\n                                                        value: editForm.phone,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                phone: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: patient.phone || 'Não informado'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"email\",\n                                                        children: \"E-mail\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"email\",\n                                                        type: \"email\",\n                                                        value: editForm.email,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                email: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: patient.email || 'Não informado'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"address\",\n                                                        children: \"Endere\\xe7o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                        id: \"address\",\n                                                        value: editForm.address,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                address: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4 text-muted-foreground mt-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: patient.address || 'Não informado'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"notes\",\n                                                        children: \"Observa\\xe7\\xf5es\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                        id: \"notes\",\n                                                        value: editForm.notes,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                notes: e.target.value\n                                                            }),\n                                                        rows: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: patient.notes || 'Nenhuma observação'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"consultas\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"Hist\\xf3rico de Consultas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: [\n                                                appointments.length,\n                                                \" consulta(s) registrada(s)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: appointments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Nenhuma consulta registrada para este paciente\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: appointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 rounded-lg border bg-card/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateBR)(appointment.start_time)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 650,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatTimeBR)(appointment.start_time)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 653,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium\",\n                                                                        children: appointment.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 658,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.getAppointmentTypeBR)(appointment.type)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 659,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: appointment.healthcare_professional_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 663,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: appointment.status === 'in_progress' ? 'default' : appointment.status === 'completed' ? 'secondary' : appointment.status === 'cancelled' ? 'destructive' : 'outline',\n                                                                children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.getAppointmentStatusBR)(appointment.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            appointment.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: [\n                                                                    \"R$ \",\n                                                                    appointment.price.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenMedicalRecord(appointment),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \"Prontu\\xe1rio\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, appointment.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 628,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"anexos\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        children: \"Anexos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        children: [\n                                                            attachments.length,\n                                                            \" arquivo(s) anexado(s)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                open: uploadDialogOpen,\n                                                onOpenChange: setUploadDialogOpen,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Adicionar Arquivo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                        children: \"Enviar Arquivo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                                                        children: \"Selecione um arquivo para anexar ao prontu\\xe1rio do paciente\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                            htmlFor: \"file\",\n                                                                            children: \"Arquivo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 726,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            id: \"file\",\n                                                                            type: \"file\",\n                                                                            onChange: (e)=>{\n                                                                                var _e_target_files;\n                                                                                return setSelectedFile(((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 727,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 725,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 724,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogFooter, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>setUploadDialogOpen(false),\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 735,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        onClick: handleFileUpload,\n                                                                        disabled: !selectedFile,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 739,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \"Enviar\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 738,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 702,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: attachments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Nenhum arquivo anexado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 rounded-lg border bg-card/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-8 w-8 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium\",\n                                                                        children: attachment.file_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 763,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            formatFileSize(attachment.file_size),\n                                                                            \" • \",\n                                                                            (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateTimeBR)(attachment.created_at)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 764,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDownloadAttachment(attachment.id, attachment.file_name),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 775,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \"Baixar\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 770,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"destructive\",\n                                                                onClick: ()=>handleDeleteAttachment(attachment.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 783,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, attachment.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 747,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 701,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 700,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                lineNumber: 489,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: medicalRecordOpen,\n                onOpenChange: setMedicalRecordOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setMedicalRecordOpen(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 800,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                children: \"Prontu\\xe1rio M\\xe9dico\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 808,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                                children: selectedAppointment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        selectedAppointment.title,\n                                                        \" - \",\n                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateTimeBR)(selectedAppointment.start_time)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 809,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 807,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 799,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                selectedAppointment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: \"Informa\\xe7\\xf5es da Consulta\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 825,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 824,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Data\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateBR)(selectedAppointment.start_time)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 831,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 829,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Hor\\xe1rio\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: [\n                                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatTimeBR)(selectedAppointment.start_time),\n                                                                        \" - \",\n                                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatTimeBR)(selectedAppointment.end_time)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 835,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 833,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Tipo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 838,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.getAppointmentTypeBR)(selectedAppointment.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 842,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: selectedAppointment.status === 'confirmed' ? 'default' : selectedAppointment.status === 'completed' ? 'secondary' : selectedAppointment.status === 'cancelled' ? 'destructive' : selectedAppointment.status === 'in_progress' ? 'default' : 'outline',\n                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.getAppointmentStatusBR)(selectedAppointment.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 841,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 828,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                selectedAppointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Profissional\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 856,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: selectedAppointment.healthcare_professional_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 857,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                selectedAppointment.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Descri\\xe7\\xe3o\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 862,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: selectedAppointment.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 823,\n                                    columnNumber: 15\n                                }, undefined),\n                                selectedAppointment && selectedAppointment.status === 'confirmed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"pt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleStartTreatment,\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Iniciar Atendimento\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 874,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 873,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 872,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Hist\\xf3rico de Registros\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 885,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Registros m\\xe9dicos desta consulta\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: medicalRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Nenhum registro m\\xe9dico encontrado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 892,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: medicalRecords.map((record, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-l-4 border-primary pl-4 py-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateTimeBR)(record.created_at)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 901,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs\",\n                                                                        children: record.created_by_name || 'Sistema'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 904,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 900,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground whitespace-pre-wrap\",\n                                                                children: record.notes\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 908,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 899,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 897,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 883,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedAppointment && (selectedAppointment.status === 'in_progress' || selectedAppointment.status === 'confirmed') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Adicionar Registro\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Adicione observa\\xe7\\xf5es e anota\\xe7\\xf5es sobre esta consulta\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 923,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 921,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                    placeholder: \"Digite suas observa\\xe7\\xf5es sobre a consulta...\",\n                                                    value: newRecord,\n                                                    onChange: (e)=>setNewRecord(e.target.value),\n                                                    rows: 4\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 928,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    onClick: handleAddMedicalRecord,\n                                                    disabled: !newRecord.trim(),\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 939,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Adicionar Registro\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 927,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 920,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 820,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                    lineNumber: 797,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                lineNumber: 796,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n        lineNumber: 456,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PatientDetailPage, \"ZfrmVYD7nw2p5VZE5EbwWPcxtgU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast\n    ];\n});\n_c = PatientDetailPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PatientDetailPage);\nvar _c;\n$RefreshReg$(_c, \"PatientDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/pacientes/[id]/page.tsx\n"));

/***/ })

});