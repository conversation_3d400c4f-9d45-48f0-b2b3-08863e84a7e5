"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/usuarios/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/admin/usuarios/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/admin/usuarios/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UsersAdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Settings,Shield,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction UsersAdminPage() {\n    _s();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDialogOpen, setIsDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('users');\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    // Form states\n    const [userForm, setUserForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        name: '',\n        phone: '',\n        password: '',\n        role: 'secretary',\n        is_active: true\n    });\n    const [roleForm, setRoleForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        user_id: '',\n        role_name: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersAdminPage.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"UsersAdminPage.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            setLoading(true);\n            await Promise.all([\n                fetchUsers(),\n                fetchUserRoles(),\n                fetchPermissions()\n            ]);\n        } catch (error) {\n            console.error('Error fetching data:', error);\n            toast({\n                title: \"Erro ao carregar dados\",\n                description: \"Não foi possível carregar os dados dos usuários.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchUsers = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_12__.makeAuthenticatedRequest)('/api/admin/users');\n            if (response.ok) {\n                const result = await response.json();\n                setUsers(result.data || []);\n            }\n        } catch (error) {\n            console.error('Error fetching users:', error);\n        }\n    };\n    const fetchUserRoles = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_12__.makeAuthenticatedRequest)('/api/user-roles');\n            if (response.ok) {\n                const result = await response.json();\n                setUserRoles(result.data || []);\n            }\n        } catch (error) {\n            console.error('Error fetching user roles:', error);\n        }\n    };\n    const fetchPermissions = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_12__.makeAuthenticatedRequest)('/api/permissions');\n            if (response.ok) {\n                const result = await response.json();\n                setPermissions(result.data || []);\n            }\n        } catch (error) {\n            console.error('Error fetching permissions:', error);\n        }\n    };\n    const handleCreateUser = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_12__.makeAuthenticatedRequest)('/api/admin/users', {\n                method: 'POST',\n                body: JSON.stringify(userForm)\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Usuário criado\",\n                    description: \"Usuário criado com sucesso.\"\n                });\n                setIsDialogOpen(false);\n                setUserForm({\n                    email: '',\n                    name: '',\n                    phone: '',\n                    password: '',\n                    role: 'secretary',\n                    is_active: true\n                });\n                fetchUsers();\n            } else {\n                throw new Error('Failed to create user');\n            }\n        } catch (error) {\n            console.error('Error creating user:', error);\n            toast({\n                title: \"Erro ao criar usuário\",\n                description: \"Não foi possível criar o usuário.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAssignRole = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_12__.makeAuthenticatedRequest)('/api/user-roles', {\n                method: 'POST',\n                body: JSON.stringify(roleForm)\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Role atribuído\",\n                    description: \"Role atribuído com sucesso ao usuário.\"\n                });\n                setRoleForm({\n                    user_id: '',\n                    role_name: ''\n                });\n                fetchUserRoles();\n            } else {\n                throw new Error('Failed to assign role');\n            }\n        } catch (error) {\n            console.error('Error assigning role:', error);\n            toast({\n                title: \"Erro ao atribuir role\",\n                description: \"Não foi possível atribuir o role ao usuário.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleToggleUserStatus = async (userId, isActive)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_12__.makeAuthenticatedRequest)(\"/api/admin/users/\".concat(userId), {\n                method: 'PATCH',\n                body: JSON.stringify({\n                    is_active: !isActive\n                })\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Status atualizado\",\n                    description: \"Usu\\xe1rio \".concat(!isActive ? 'ativado' : 'desativado', \" com sucesso.\")\n                });\n                fetchUsers();\n            } else {\n                throw new Error('Failed to update user status');\n            }\n        } catch (error) {\n            console.error('Error updating user status:', error);\n            toast({\n                title: \"Erro ao atualizar status\",\n                description: \"Não foi possível atualizar o status do usuário.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const getUserRoles = (userId)=>{\n        return userRoles.filter((ur)=>ur.user_id === userId).map((ur)=>ur.role_name);\n    };\n    const getRolePermissions = (roleName)=>{\n        return permissions.filter((p)=>p.role_name === roleName);\n    };\n    const availableRoles = [\n        'admin',\n        'doctor',\n        'secretary'\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-muted-foreground\",\n                            children: \"Carregando...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                lineNumber: 228,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"Administra\\xe7\\xe3o de Usu\\xe1rios\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Gerencie usu\\xe1rios, roles e permiss\\xf5es do sistema\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                        open: isDialogOpen,\n                        onOpenChange: setIsDialogOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Novo Usu\\xe1rio\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                                children: \"Criar Novo Usu\\xe1rio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                                children: \"Adicione um novo usu\\xe1rio ao sistema\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"email\",\n                                                        children: \"E-mail *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"email\",\n                                                        type: \"email\",\n                                                        value: userForm.email,\n                                                        onChange: (e)=>setUserForm({\n                                                                ...userForm,\n                                                                email: e.target.value\n                                                            }),\n                                                        placeholder: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"name\",\n                                                        children: \"Nome\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"name\",\n                                                        value: userForm.name,\n                                                        onChange: (e)=>setUserForm({\n                                                                ...userForm,\n                                                                name: e.target.value\n                                                            }),\n                                                        placeholder: \"Nome completo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"phone\",\n                                                        children: \"Telefone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"phone\",\n                                                        value: userForm.phone,\n                                                        onChange: (e)=>setUserForm({\n                                                                ...userForm,\n                                                                phone: e.target.value\n                                                            }),\n                                                        placeholder: \"(11) 99999-9999\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"password\",\n                                                        children: \"Senha Inicial *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"password\",\n                                                        type: \"password\",\n                                                        value: userForm.password,\n                                                        onChange: (e)=>setUserForm({\n                                                                ...userForm,\n                                                                password: e.target.value\n                                                            }),\n                                                        placeholder: \"Senha tempor\\xe1ria para primeiro acesso\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"role\",\n                                                        children: \"Role Inicial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        value: userForm.role,\n                                                        onValueChange: (value)=>setUserForm({\n                                                                ...userForm,\n                                                                role: value\n                                                            }),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: availableRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: role,\n                                                                        children: role.charAt(0).toUpperCase() + role.slice(1)\n                                                                    }, role, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogFooter, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>setIsDialogOpen(false),\n                                                children: \"Cancelar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleCreateUser,\n                                                children: \"Criar Usu\\xe1rio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.Tabs, {\n                value: activeTab,\n                onValueChange: setActiveTab,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsList, {\n                        className: \"grid w-full grid-cols-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"users\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Usu\\xe1rios\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"roles\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Roles\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                value: \"permissions\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Permiss\\xf5es\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"users\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            children: \"Usu\\xe1rios do Sistema\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Lista de todos os usu\\xe1rios cadastrados no sistema\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                            children: \"Nome\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                            children: \"E-mail\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                            children: \"Telefone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                            children: \"Roles\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                            children: \"A\\xe7\\xf5es\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                                children: users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                className: \"font-medium\",\n                                                                children: user.name || 'Sem nome'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                children: user.phone || '-'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: getUserRoles(user.id).map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: role\n                                                                        }, role, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: user.is_active ? \"default\" : \"destructive\",\n                                                                    children: user.is_active ? 'Ativo' : 'Inativo'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>setSelectedUser(user),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Settings_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                                lineNumber: 394,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>handleToggleUserStatus(user.id, user.is_active),\n                                                                            children: user.is_active ? 'Desativar' : 'Ativar'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                            lineNumber: 396,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, user.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"roles\",\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Atribui\\xe7\\xe3o de Roles\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Atribua roles aos usu\\xe1rios do sistema\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"user_select\",\n                                                                children: \"Usu\\xe1rio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                value: roleForm.user_id,\n                                                                onValueChange: (value)=>setRoleForm({\n                                                                        ...roleForm,\n                                                                        user_id: value\n                                                                    }),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Selecione um usu\\xe1rio\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                            lineNumber: 427,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: user.id,\n                                                                                children: user.name || user.email\n                                                                            }, user.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                                lineNumber: 431,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"role_select\",\n                                                                children: \"Role\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                value: roleForm.role_name,\n                                                                onValueChange: (value)=>setRoleForm({\n                                                                        ...roleForm,\n                                                                        role_name: value\n                                                                    }),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Selecione um role\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                            lineNumber: 442,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: availableRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: role,\n                                                                                children: role.charAt(0).toUpperCase() + role.slice(1)\n                                                                            }, role, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                                lineNumber: 446,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleAssignRole,\n                                                disabled: !roleForm.user_id || !roleForm.role_name,\n                                                children: \"Atribuir Role\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Roles Atribu\\xeddos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Lista de todos os roles atribu\\xeddos aos usu\\xe1rios\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                                children: \"Usu\\xe1rio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                                children: \"E-mail\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                                children: \"Role\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                                children: \"Data de Atribui\\xe7\\xe3o\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                                    children: userRoles.map((userRole)=>{\n                                                        const user = users.find((u)=>u.id === userRole.user_id);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                    children: (user === null || user === void 0 ? void 0 : user.name) || 'Usuário não encontrado'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                    children: (user === null || user === void 0 ? void 0 : user.email) || '-'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        children: userRole.role_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                        lineNumber: 485,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                                    children: new Date(userRole.created_at).toLocaleDateString('pt-BR')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, userRole.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                        value: \"permissions\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            children: \"Matriz de Permiss\\xf5es\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Visualize as permiss\\xf5es de cada role no sistema\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: availableRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold capitalize\",\n                                                        children: role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2\",\n                                                        children: getRolePermissions(role).map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                variant: \"outline\",\n                                                                children: [\n                                                                    permission.resource,\n                                                                    \":\",\n                                                                    permission.action\n                                                                ]\n                                                            }, \"\".concat(permission.resource, \"-\").concat(permission.action), true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    getRolePermissions(role).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"Nenhuma permiss\\xe3o atribu\\xedda\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, role, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\usuarios\\\\page.tsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, this);\n}\n_s(UsersAdminPage, \"1NZCZHe5qk8YbkaAliY57TO8gyk=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast\n    ];\n});\n_c = UsersAdminPage;\nvar _c;\n$RefreshReg$(_c, \"UsersAdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/admin/usuarios/page.tsx\n"));

/***/ })

});