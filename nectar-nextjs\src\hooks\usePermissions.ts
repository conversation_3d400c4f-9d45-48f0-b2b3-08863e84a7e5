import { useState, useEffect } from 'react'
import { makeAuthenticatedRequest } from '@/lib/auth'

interface Permission {
  id: string
  role_name: string
  resource: string
  action: string
}

interface UserRole {
  id: string
  user_id: string
  role_name: string
  role_id?: string
}

export function usePermissions() {
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [userRoles, setUserRoles] = useState<UserRole[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        
        // Fetch user roles
        const rolesResponse = await makeAuthenticatedRequest('/api/user-roles')
        if (rolesResponse.ok) {
          const rolesResult = await rolesResponse.json()
          setUserRoles(rolesResult.data || [])
        }

        // Fetch permissions
        const permissionsResponse = await makeAuthenticatedRequest('/api/permissions')
        if (permissionsResponse.ok) {
          const permissionsResult = await permissionsResponse.json()
          setPermissions(permissionsResult.data || [])
        }
      } catch (error) {
        console.error('Error fetching permissions:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const hasPermission = (resource: string, action: string): boolean => {
    if (loading) return false
    
    // Get user's role names
    const roleNames = userRoles.map(role => role.role_name)
    
    // Check if any of the user's roles have the required permission
    return permissions.some(permission => 
      roleNames.includes(permission.role_name) &&
      permission.resource === resource &&
      permission.action === action
    )
  }

  const hasRole = (roleName: string): boolean => {
    if (loading) return false
    return userRoles.some(role => role.role_name === roleName)
  }

  const isAdmin = (): boolean => {
    return hasRole('admin')
  }

  const isDoctor = (): boolean => {
    return hasRole('doctor')
  }

  const isSecretary = (): boolean => {
    return hasRole('secretary')
  }

  const canViewUsers = (): boolean => {
    return hasPermission('users', 'view')
  }

  const canViewMedicalRecords = (): boolean => {
    return hasPermission('medical_records', 'view')
  }

  return {
    permissions,
    userRoles,
    loading,
    hasPermission,
    hasRole,
    isAdmin,
    isDoctor,
    isSecretary,
    canViewUsers,
    canViewMedicalRecords
  }
}

export function useHasPermission(resource: string, action: string) {
  const { hasPermission, loading } = usePermissions()
  return { hasPermission: hasPermission(resource, action), loading }
}

export function useIsAdmin() {
  const { isAdmin, loading } = usePermissions()
  return { isAdmin: isAdmin(), loading }
}

export function useCanViewUsers() {
  const { canViewUsers, loading } = usePermissions()
  return { canViewUsers: canViewUsers(), loading }
}

export function useCanViewMedicalRecords() {
  const { canViewMedicalRecords, loading } = usePermissions()
  return { canViewMedicalRecords: canViewMedicalRecords(), loading }
}
