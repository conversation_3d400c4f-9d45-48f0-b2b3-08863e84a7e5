"use client"

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Settings, User, Bell, Shield, Smartphone, Mail, Clock, Users, Building } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { makeAuthenticatedRequest } from '@/lib/api-client';

type UserProfile = {
  name: string;
  email: string;
  phone: string;
  specialty: string;
  crm: string;
  clinic_name: string;
  clinic_address: string;
};

type NotificationSettings = {
  email_appointments: boolean;
  email_messages: boolean;
  sms_appointments: boolean;
  sms_messages: boolean;
  whatsapp_notifications: boolean;
};

type IntegrationSettings = {
  whatsapp_token: string;
  whatsapp_phone: string;
  email_smtp_host: string;
  email_smtp_port: string;
  email_smtp_user: string;
  email_smtp_password: string;
};

type ClinicSettings = {
  clinic_name: string | null;
  working_hours_start: string;
  working_hours_end: string;
  working_days: number[];
  appointment_duration_minutes: number;
  allow_weekend_appointments: boolean;
  timezone: string;
};

type HealthcareProfessional = {
  id: string;
  name: string;
  specialty: string | null;
  crm: string | null;
  phone: string | null;
  email: string | null;
  is_active: boolean;
};

type UserRole = {
  id: string;
  user_id: string;
  role_name: string;
  created_at: string;
};

const SettingsPage = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();

  // Estados iniciais com valores padrão
  const defaultProfile: UserProfile = {
    name: '',
    email: '',
    phone: '',
    specialty: '',
    crm: '',
    clinic_name: '',
    clinic_address: ''
  };

  const defaultNotifications: NotificationSettings = {
    email_appointments: true,
    email_messages: true,
    sms_appointments: false,
    sms_messages: false,
    whatsapp_notifications: true
  };

  const defaultIntegrations: IntegrationSettings = {
    whatsapp_token: '',
    whatsapp_phone: '',
    email_smtp_host: '',
    email_smtp_port: '',
    email_smtp_user: '',
    email_smtp_password: ''
  };

  const defaultClinicSettings: ClinicSettings = {
    clinic_name: null,
    working_hours_start: '08:00',
    working_hours_end: '18:00',
    working_days: [1, 2, 3, 4, 5], // Monday to Friday
    appointment_duration_minutes: 30,
    allow_weekend_appointments: false,
    timezone: 'America/Sao_Paulo'
  };

  const [profile, setProfile] = useState<UserProfile>(defaultProfile);
  const [notifications, setNotifications] = useState<NotificationSettings>(defaultNotifications);
  const [integrations, setIntegrations] = useState<IntegrationSettings>(defaultIntegrations);
  const [clinicSettings, setClinicSettings] = useState<ClinicSettings>(defaultClinicSettings);
  const [healthcareProfessionals, setHealthcareProfessionals] = useState<HealthcareProfessional[]>([]);
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);

  useEffect(() => {
    fetchSettings();
    fetchClinicSettings();
    fetchHealthcareProfessionals();
    fetchUserRoles();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await makeAuthenticatedRequest('/api/settings');
      if (!response.ok) throw new Error('Failed to fetch settings');
      const result = await response.json();
      const data = result.data || result;

      if (data.profile) setProfile(data.profile);
      if (data.notifications) setNotifications(data.notifications);
      if (data.integrations) setIntegrations(data.integrations);
    } catch (error) {
      toast({
        title: "Erro ao carregar configurações",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchClinicSettings = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/clinic-settings');
      if (!response.ok) throw new Error('Failed to fetch clinic settings');
      const result = await response.json();
      const data = result.data || result;
      setClinicSettings(data);
    } catch (error) {
      console.error('Error fetching clinic settings:', error);
    }
  };

  const fetchHealthcareProfessionals = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/healthcare-professionals');
      if (!response.ok) throw new Error('Failed to fetch healthcare professionals');
      const result = await response.json();
      const data = result.data || result;
      setHealthcareProfessionals(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching healthcare professionals:', error);
    }
  };

  const fetchUserRoles = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/user-roles');
      if (!response.ok) throw new Error('Failed to fetch user roles');
      const result = await response.json();
      const data = result.data || result;
      setUserRoles(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching user roles:', error);
    }
  };

  const saveProfile = async () => {
    try {
      setSaving(true);
      const response = await makeAuthenticatedRequest('/api/settings/profile', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(profile)
      });

      if (!response.ok) throw new Error('Failed to save profile');

      toast({
        title: "Perfil atualizado",
        description: "Suas informações foram salvas com sucesso"
      });
    } catch (error) {
      toast({
        title: "Erro ao salvar perfil",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const saveClinicSettings = async () => {
    try {
      setSaving(true);
      const response = await makeAuthenticatedRequest('/api/clinic-settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(clinicSettings)
      });

      if (!response.ok) throw new Error('Failed to save clinic settings');

      toast({
        title: "Configurações da clínica atualizadas",
        description: "Suas configurações foram salvas com sucesso"
      });
    } catch (error) {
      toast({
        title: "Erro ao salvar configurações",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };


  const saveIntegrations = async () => {
    try {
      setSaving(true);
      const response = await fetch('/api/settings/integrations', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(integrations)
      });

      if (!response.ok) throw new Error('Failed to save integrations');

      toast({
        title: "Integrações atualizadas",
        description: "Suas configurações foram salvas com sucesso"
      });
    } catch (error) {
      toast({
        title: "Erro ao salvar integrações",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground">Configurações</h1>
        <p className="text-muted-foreground">Gerencie suas preferências e integrações</p>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="profile" className="flex items-center">
            <User className="mr-2 h-4 w-4" />
            Perfil
          </TabsTrigger>
          <TabsTrigger value="clinic" className="flex items-center">
            <Building className="mr-2 h-4 w-4" />
            Clínica
          </TabsTrigger>
          <TabsTrigger value="professionals" className="flex items-center">
            <Users className="mr-2 h-4 w-4" />
            Profissionais
          </TabsTrigger>
          <TabsTrigger value="integrations" className="flex items-center">
            <Smartphone className="mr-2 h-4 w-4" />
            Integrações
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center">
            <Shield className="mr-2 h-4 w-4" />
            Segurança
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5 text-primary" />
                Informações Pessoais
              </CardTitle>
              <CardDescription>
                Atualize suas informações pessoais e profissionais
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome Completo</Label>
                  <Input
                    id="name"
                    value={profile.name}
                    onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={profile.email}
                    onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Telefone</Label>
                  <Input
                    id="phone"
                    value={profile.phone}
                    onChange={(e) => setProfile({ ...profile, phone: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="specialty">Especialidade</Label>
                  <Input
                    id="specialty"
                    value={profile.specialty}
                    onChange={(e) => setProfile({ ...profile, specialty: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="crm">CRM</Label>
                  <Input
                    id="crm"
                    value={profile.crm}
                    onChange={(e) => setProfile({ ...profile, crm: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="clinic_name">Nome da Clínica</Label>
                  <Input
                    id="clinic_name"
                    value={profile.clinic_name}
                    onChange={(e) => setProfile({ ...profile, clinic_name: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="clinic_address">Endereço da Clínica</Label>
                <Textarea
                  id="clinic_address"
                  value={profile.clinic_address}
                  onChange={(e) => setProfile({ ...profile, clinic_address: e.target.value })}
                />
              </div>

              <Button onClick={saveProfile} disabled={saving}>
                {saving ? 'Salvando...' : 'Salvar Perfil'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="clinic">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building className="mr-2 h-5 w-5 text-primary" />
                Configurações da Clínica
              </CardTitle>
              <CardDescription>
                Configure horários de funcionamento e preferências da clínica
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="clinic_name">Nome da Clínica</Label>
                <Input
                  id="clinic_name"
                  value={clinicSettings.clinic_name || ''}
                  onChange={(e) => setClinicSettings({ ...clinicSettings, clinic_name: e.target.value })}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="working_hours_start">Horário de Início</Label>
                  <Input
                    id="working_hours_start"
                    type="time"
                    value={clinicSettings.working_hours_start}
                    onChange={(e) => setClinicSettings({ ...clinicSettings, working_hours_start: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="working_hours_end">Horário de Término</Label>
                  <Input
                    id="working_hours_end"
                    type="time"
                    value={clinicSettings.working_hours_end}
                    onChange={(e) => setClinicSettings({ ...clinicSettings, working_hours_end: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Dias de Funcionamento</Label>
                <div className="flex gap-2">
                  {[
                    { value: 1, label: 'Dom' },
                    { value: 2, label: 'Seg' },
                    { value: 3, label: 'Ter' },
                    { value: 4, label: 'Qua' },
                    { value: 5, label: 'Qui' },
                    { value: 6, label: 'Sex' },
                    { value: 7, label: 'Sáb' }
                  ].map(day => (
                    <Button
                      key={day.value}
                      type="button"
                      variant={clinicSettings.working_days.includes(day.value) ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => {
                        const newDays = clinicSettings.working_days.includes(day.value)
                          ? clinicSettings.working_days.filter(d => d !== day.value)
                          : [...clinicSettings.working_days, day.value];
                        setClinicSettings({ ...clinicSettings, working_days: newDays });
                      }}
                    >
                      {day.label}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="appointment_duration">Duração Padrão da Consulta (minutos)</Label>
                  <Input
                    id="appointment_duration"
                    type="number"
                    min="15"
                    max="180"
                    step="15"
                    value={clinicSettings.appointment_duration_minutes}
                    onChange={(e) => setClinicSettings({
                      ...clinicSettings,
                      appointment_duration_minutes: parseInt(e.target.value) || 30
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timezone">Fuso Horário</Label>
                  <Input
                    id="timezone"
                    value={clinicSettings.timezone}
                    onChange={(e) => setClinicSettings({ ...clinicSettings, timezone: e.target.value })}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="allow_weekend"
                  checked={clinicSettings.allow_weekend_appointments}
                  onCheckedChange={(checked) =>
                    setClinicSettings({ ...clinicSettings, allow_weekend_appointments: checked })
                  }
                />
                <Label htmlFor="allow_weekend">Permitir agendamentos nos fins de semana</Label>
              </div>

              <Button onClick={saveClinicSettings} disabled={saving}>
                {saving ? 'Salvando...' : 'Salvar Configurações'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="professionals">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="mr-2 h-5 w-5 text-primary" />
                Profissionais de Saúde
              </CardTitle>
              <CardDescription>
                Gerencie os profissionais de saúde da sua clínica
              </CardDescription>
            </CardHeader>
            <CardContent>
              {healthcareProfessionals.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>Nenhum profissional cadastrado</p>
                  <Button className="mt-4">
                    Adicionar Profissional
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {healthcareProfessionals.map((professional) => (
                    <div
                      key={professional.id}
                      className="flex items-center justify-between p-4 rounded-lg border bg-card/50"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                          <User className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-medium">{professional.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {professional.specialty || 'Especialidade não informada'}
                          </p>
                          {professional.crm && (
                            <p className="text-xs text-muted-foreground">CRM: {professional.crm}</p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={professional.is_active ? 'default' : 'secondary'}>
                          {professional.is_active ? 'Ativo' : 'Inativo'}
                        </Badge>
                        <Button size="sm" variant="outline">
                          Editar
                        </Button>
                      </div>
                    </div>
                  ))}
                  <Button>
                    <User className="mr-2 h-4 w-4" />
                    Adicionar Profissional
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Smartphone className="mr-2 h-5 w-5 text-primary" />
                Integrações
              </CardTitle>
              <CardDescription>
                Configure integrações com WhatsApp e email
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h4 className="text-sm font-medium">WhatsApp Business API</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="whatsapp_token">Token de Acesso</Label>
                    <Input
                      id="whatsapp_token"
                      type="password"
                      value={integrations.whatsapp_token}
                      onChange={(e) => setIntegrations({ ...integrations, whatsapp_token: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="whatsapp_phone">Número do WhatsApp</Label>
                    <Input
                      id="whatsapp_phone"
                      value={integrations.whatsapp_phone}
                      onChange={(e) => setIntegrations({ ...integrations, whatsapp_phone: e.target.value })}
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium">Configurações de Email</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email_smtp_host">Servidor SMTP</Label>
                    <Input
                      id="email_smtp_host"
                      value={integrations.email_smtp_host}
                      onChange={(e) => setIntegrations({ ...integrations, email_smtp_host: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email_smtp_port">Porta SMTP</Label>
                    <Input
                      id="email_smtp_port"
                      value={integrations.email_smtp_port}
                      onChange={(e) => setIntegrations({ ...integrations, email_smtp_port: e.target.value })}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email_smtp_user">Usuário SMTP</Label>
                    <Input
                      id="email_smtp_user"
                      value={integrations.email_smtp_user}
                      onChange={(e) => setIntegrations({ ...integrations, email_smtp_user: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email_smtp_password">Senha SMTP</Label>
                    <Input
                      id="email_smtp_password"
                      type="password"
                      value={integrations.email_smtp_password}
                      onChange={(e) => setIntegrations({ ...integrations, email_smtp_password: e.target.value })}
                    />
                  </div>
                </div>
              </div>

              <Button onClick={saveIntegrations} disabled={saving}>
                {saving ? 'Salvando...' : 'Salvar Integrações'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5 text-primary" />
                Segurança
              </CardTitle>
              <CardDescription>
                Gerencie configurações de segurança da sua conta
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h4 className="text-sm font-medium">Alterar Senha</h4>
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="current_password">Senha Atual</Label>
                    <Input id="current_password" type="password" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="new_password">Nova Senha</Label>
                    <Input id="new_password" type="password" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirm_password">Confirmar Nova Senha</Label>
                    <Input id="confirm_password" type="password" />
                  </div>
                </div>
                <Button variant="outline">
                  Alterar Senha
                </Button>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium">Autenticação de Dois Fatores</h4>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm">Ativar 2FA</p>
                    <p className="text-sm text-muted-foreground">
                      Adicione uma camada extra de segurança à sua conta
                    </p>
                  </div>
                  <Switch />
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium">Sessões Ativas</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="text-sm font-medium">Sessão atual</p>
                      <p className="text-sm text-muted-foreground">Windows • Chrome • São Paulo, BR</p>
                    </div>
                    <Button variant="outline" size="sm">
                      Encerrar
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SettingsPage;
