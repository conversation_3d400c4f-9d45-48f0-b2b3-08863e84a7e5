"use client";

import { useState, useEffect, useRef, useCallback } from 'react';
import { useDashboardStats } from '@/hooks/useApiRequest';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Calendar,
  Users,
  MessageSquare,
  BarChart3,
  ArrowUp,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp
} from "lucide-react";
import { useAuth } from '@/hooks/useAuth';

interface DashboardStats {
  todayAppointments: number;
  totalPatients: number;
  unreadMessages: number;
  monthlyRevenue: number;
}

interface RecentAppointment {
  time: string;
  patient: string;
  type: string;
  status: string;
}

interface DashboardData {
  stats: DashboardStats;
  recentAppointments: RecentAppointment[];
}

export default function Dashboard() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const { user } = useAuth();
  const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastFetchRef = useRef<number>(0);
  const { execute: fetchStats, loading, error } = useDashboardStats();

  const fetchDashboardData = useCallback(async () => {
    const now = Date.now();

    // Debounce: prevent calls within 2 seconds of each other
    if (now - lastFetchRef.current < 2000) {
      console.log('[DASHBOARD DEBUG] Debounced - too soon since last fetch');
      return;
    }

    // Prevent multiple simultaneous calls
    if (loading) {
      console.log('[DASHBOARD DEBUG] Already loading, skipping fetch');
      return;
    }

    lastFetchRef.current = now;

    try {
      console.log('[DASHBOARD DEBUG] Starting fetchDashboardData');

      const data = await fetchStats('/api/dashboard/stats');

      if (data) {
        console.log('[DASHBOARD DEBUG] API response data:', data);
        setDashboardData(data);
      } else {
        console.log('[DASHBOARD DEBUG] No data received or request failed');
      }
    } catch (error) {
      console.error('[DASHBOARD DEBUG] Fetch error:', error);
    }
  }, [loading, fetchStats]);

  useEffect(() => {
    console.log('[DASHBOARD DEBUG] useEffect triggered, user:', !!user, 'loading:', loading);

    // Clear any existing timeout
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current);
    }

    if (user) {
      console.log('[DASHBOARD DEBUG] Scheduling fetchDashboardData with timeout');
      // Debounce the fetch call
      fetchTimeoutRef.current = setTimeout(() => {
        console.log('[DASHBOARD DEBUG] Timeout executed, calling fetchDashboardData');
        fetchDashboardData();
      }, 500); // 500ms debounce
    }

    // Cleanup function
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, [user, fetchDashboardData]);

  const recentActivities = [
    { type: "message", text: "Nova mensagem recebida", time: "2 min atrás" },
    { type: "appointment", text: "Consulta confirmada", time: "15 min atrás" },
    { type: "payment", text: "Pagamento recebido - R$ 150", time: "1 hora atrás" },
    { type: "reminder", text: "Lembrete enviado", time: "2 horas atrás" },
    { type: "appointment", text: "Nova consulta agendada", time: "3 horas atrás" }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const stats = dashboardData?.stats || {
    todayAppointments: 0,
    totalPatients: 0,
    unreadMessages: 0,
    monthlyRevenue: 0
  };

  const recentAppointments = dashboardData?.recentAppointments || [];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
        <p className="text-muted-foreground">Visão geral dos seus atendimentos e métricas</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Consultas Hoje</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{stats.todayAppointments}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <ArrowUp className="h-3 w-3 mr-1" />
              Consultas do dia
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pacientes Ativos</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{stats.totalPatients}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <ArrowUp className="h-3 w-3 mr-1" />
              Total cadastrados
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Mensagens</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">23</div>
            <p className="text-xs text-yellow-600 flex items-center mt-1">
              <AlertCircle className="h-3 w-3 mr-1" />
              {stats.unreadMessages} não lidas
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Receita Mensal</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">R$ {stats.monthlyRevenue.toLocaleString()}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              +15% vs mês anterior
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid lg:grid-cols-3 gap-6">
        {/* Próximas Consultas */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="mr-2 h-5 w-5 text-primary" />
                Próximas Consultas
              </CardTitle>
              <CardDescription>Agendamentos para hoje e amanhã</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentAppointments.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>Nenhuma consulta agendada para hoje</p>
                </div>
               ) : (
                 recentAppointments.map((appointment, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg border bg-card/50">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10">
                      <Clock className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                    <p className="font-medium text-foreground">{appointment.patient}</p>
                      <p className="text-sm text-muted-foreground">{appointment.type}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-foreground">{appointment.time}</span>
                    {appointment.status === 'confirmed' ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-yellow-500" />
                    )}
                  </div>
                </div>
              )))}
            </CardContent>
          </Card>
        </div>

        {/* Activity Feed */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageSquare className="mr-2 h-5 w-5 text-primary" />
                Atividades Recentes
              </CardTitle>
              <CardDescription>Últimas interações</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-start space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                  <div
                    className={`w-2 h-2 rounded-full mt-2 ${
                      activity.type === 'message'
                        ? 'bg-blue-500'
                        : (activity.type === 'appointment'
                          ? 'bg-green-500'
                          : (activity.type === 'payment'
                            ? 'bg-primary'
                            : 'bg-yellow-500'
                        )
                        )
                    }`}
                  />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-foreground">{activity.text}</p>
                    <p className="text-xs text-muted-foreground">{activity.time}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Ações Rápidas</CardTitle>
          <CardDescription>Principais funcionalidades ao seu alcance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col space-y-2">
              <Calendar className="h-6 w-6" />
              <span>Nova Consulta</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col space-y-2">
              <Users className="h-6 w-6" />
              <span>Cadastrar Paciente</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col space-y-2">
              <MessageSquare className="h-6 w-6" />
              <span>Enviar Mensagem</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col space-y-2">
              <BarChart3 className="h-6 w-6" />
              <span>Ver Relatórios</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
