"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/AppSidebar.tsx":
/*!***************************************!*\
  !*** ./src/components/AppSidebar.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,LayoutDashboard,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,LayoutDashboard,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,LayoutDashboard,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,LayoutDashboard,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,LayoutDashboard,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,LayoutDashboard,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst sidebarItems = [\n    {\n        title: \"Dashboard\",\n        url: \"/dashboard\",\n        icon: _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        title: \"Agenda\",\n        url: \"/dashboard/agenda\",\n        icon: _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        title: \"Pacientes\",\n        url: \"/dashboard/pacientes\",\n        icon: _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        title: \"Configurações\",\n        url: \"/dashboard/configuracoes\",\n        icon: _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    }\n];\nconst adminItems = [\n    {\n        title: \"Usuários\",\n        url: \"/dashboard/admin/usuarios\",\n        icon: _barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    }\n];\nfunction AppSidebar() {\n    _s();\n    const { state } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isAdmin, loading, userRoles } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    // Debug log\n    console.log('🔍 Sidebar permissions:', {\n        isAdmin: isAdmin(),\n        loading,\n        userRoles\n    });\n    const isActive = (path)=>pathname === path;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n        collapsible: \"icon\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarContent, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_LayoutDashboard_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-8 w-8 text-primary mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            state === \"expanded\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-foreground\",\n                                children: \"Nectar Sa\\xfade\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 38\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroup, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroupLabel, {\n                            children: \"Menu Principal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroupContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenu, {\n                                children: sidebarItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.url,\n                                                className: isActive(item.url) ? \"bg-primary/10 text-primary font-medium border-r-2 border-primary\" : \"hover:bg-muted/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"mr-3 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    state === \"expanded\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 48\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, item.title, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                isAdmin() && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroup, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroupLabel, {\n                            children: \"Administra\\xe7\\xe3o\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroupContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenu, {\n                                children: adminItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.url,\n                                                className: isActive(item.url) ? \"bg-primary/10 text-primary font-medium border-r-2 border-primary\" : \"hover:bg-muted/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"mr-3 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    state === \"expanded\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 50\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, item.title, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppSidebar.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(AppSidebar, \"pRNY4f/X0d6BnP8jgUuXluiQpPc=\", false, function() {\n    return [\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AppSidebar.tsx\n"));

/***/ })

});