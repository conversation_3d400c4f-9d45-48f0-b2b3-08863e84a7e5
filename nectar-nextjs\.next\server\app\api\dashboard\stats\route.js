/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_cirov_Documents_next_js_nectar_nectar_nextjs_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/dashboard/stats/route.ts */ \"(rsc)/./src/app/api/dashboard/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_cirov_Documents_next_js_nectar_nectar_nextjs_src_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/dashboard/stats/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/dashboard/stats/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./src/lib/api-utils.ts\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/date-utils */ \"(rsc)/./src/lib/date-utils.ts\");\n\n\nasync function GET(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withAuth)(request, async (userId, supabase)=>{\n        try {\n            // Get today's date range\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            const tomorrow = new Date(today);\n            tomorrow.setDate(tomorrow.getDate() + 1);\n            // Fetch today's appointments count\n            const { count: todayCount } = await supabase.from('appointments').select('*', {\n                count: 'exact',\n                head: true\n            }).eq('user_id', userId).gte('start_time', today.toISOString()).lt('start_time', tomorrow.toISOString());\n            // Fetch total patients count\n            const { count: patientsCount } = await supabase.from('patients').select('*', {\n                count: 'exact',\n                head: true\n            }).eq('user_id', userId);\n            // Fetch recent appointments with patient info\n            const { data: appointments } = await supabase.from('appointments').select(`\n          *,\n          patients!inner(name)\n        `).eq('user_id', userId).gte('start_time', today.toISOString()).order('start_time').limit(5);\n            const stats = {\n                todayAppointments: todayCount || 0,\n                totalPatients: patientsCount || 0,\n                unreadMessages: 5,\n                monthlyRevenue: 8250 // Mock data for now\n            };\n            const recentAppointments = appointments?.map((apt)=>({\n                    time: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_1__.formatTimeBR)(apt.start_time),\n                    patient: apt.patients.name,\n                    type: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_1__.getAppointmentTypeBR)(apt.type),\n                    status: apt.status\n                })) || [];\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)({\n                stats,\n                recentAppointments\n            });\n        } catch (error) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/dashboard/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api-utils.ts":
/*!******************************!*\
  !*** ./src/lib/api-utils.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApiResponse: () => (/* binding */ createApiResponse),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   withAuth: () => (/* binding */ withAuth),\n/* harmony export */   withAuthAndPermission: () => (/* binding */ withAuthAndPermission)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_permissions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/permissions */ \"(rsc)/./src/lib/permissions.ts\");\n\n\n\nfunction createApiResponse(data, message, status = 200) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        data,\n        message\n    }, {\n        status\n    });\n}\nasync function withAuth(request, handler) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        console.log('🔐 Auth check:', {\n            hasUser: !!user,\n            userId: user?.id,\n            email: user?.email,\n            error: authError?.message\n        });\n        if (authError || !user) {\n            console.error('❌ Auth failed:', authError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        return await handler(user.id, supabase);\n    } catch (error) {\n        console.error('API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function withAuthAndPermission(request, resource, action, handler) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return createApiResponse(undefined, 'Unauthorized', 401);\n        }\n        // Check permissions\n        const hasAccess = await (0,_lib_permissions__WEBPACK_IMPORTED_MODULE_2__.hasPermission)(user.id, resource, action);\n        if (!hasAccess) {\n            return createApiResponse(undefined, 'Forbidden: Insufficient permissions', 403);\n        }\n        return await handler(user.id, supabase);\n    } catch (error) {\n        console.error('API Error:', error);\n        return createApiResponse(undefined, error instanceof Error ? error.message : 'Internal server error', 500);\n    }\n}\nfunction handleApiError(error) {\n    console.error('API Error:', error);\n    if (error?.code === 'PGRST116') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Resource not found'\n        }, {\n            status: 404\n        });\n    }\n    if (error?.code === '23505') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Resource already exists'\n        }, {\n            status: 409\n        });\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: error?.message || 'Internal server error'\n    }, {\n        status: 500\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2FwaS11dGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXVEO0FBQ0g7QUFDUTtBQVFyRCxTQUFTRyxrQkFDZEMsSUFBUSxFQUNSQyxPQUFnQixFQUNoQkMsU0FBaUIsR0FBRztJQUVwQixPQUFPTixxREFBWUEsQ0FBQ08sSUFBSSxDQUN0QjtRQUNFSDtRQUNBQztJQUNGLEdBQ0E7UUFBRUM7SUFBTztBQUViO0FBRU8sZUFBZUUsU0FDcEJDLE9BQW9CLEVBQ3BCQyxPQUFpRjtJQUVqRixJQUFJO1FBQ0YsTUFBTUMsV0FBVyxNQUFNVixrRUFBWUE7UUFFbkMsTUFBTSxFQUNKRyxNQUFNLEVBQUVRLElBQUksRUFBRSxFQUNkQyxPQUFPQyxTQUFTLEVBQ2pCLEdBQUcsTUFBTUgsU0FBU0ksSUFBSSxDQUFDQyxPQUFPO1FBRS9CQyxRQUFRQyxHQUFHLENBQUMsa0JBQWtCO1lBQzVCQyxTQUFTLENBQUMsQ0FBQ1A7WUFDWFEsUUFBUVIsTUFBTVM7WUFDZEMsT0FBT1YsTUFBTVU7WUFDYlQsT0FBT0MsV0FBV1Q7UUFDcEI7UUFFQSxJQUFJUyxhQUFhLENBQUNGLE1BQU07WUFDdEJLLFFBQVFKLEtBQUssQ0FBQyxrQkFBa0JDO1lBQ2hDLE9BQU9kLHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7Z0JBQUVNLE9BQU87WUFBZSxHQUFHO2dCQUFFUCxRQUFRO1lBQUk7UUFDcEU7UUFFQSxPQUFPLE1BQU1JLFFBQVFFLEtBQUtTLEVBQUUsRUFBRVY7SUFDaEMsRUFBRSxPQUFPRSxPQUFPO1FBQ2RJLFFBQVFKLEtBQUssQ0FBQyxjQUFjQTtRQUM1QixPQUFPYixxREFBWUEsQ0FBQ08sSUFBSSxDQUN0QjtZQUFFTSxPQUFPQSxpQkFBaUJVLFFBQVFWLE1BQU1SLE9BQU8sR0FBRztRQUF3QixHQUMxRTtZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVPLGVBQWVrQixzQkFDcEJmLE9BQW9CLEVBQ3BCZ0IsUUFBZ0IsRUFDaEJDLE1BQStDLEVBQy9DaEIsT0FBaUY7SUFFakYsSUFBSTtRQUNGLE1BQU1DLFdBQVcsTUFBTVYsa0VBQVlBO1FBRW5DLE1BQU0sRUFDSkcsTUFBTSxFQUFFUSxJQUFJLEVBQUUsRUFDZEMsT0FBT0MsU0FBUyxFQUNqQixHQUFHLE1BQU1ILFNBQVNJLElBQUksQ0FBQ0MsT0FBTztRQUUvQixJQUFJRixhQUFhLENBQUNGLE1BQU07WUFDdEIsT0FBT1Qsa0JBQWtCd0IsV0FBVyxnQkFBZ0I7UUFDdEQ7UUFFQSxvQkFBb0I7UUFDcEIsTUFBTUMsWUFBWSxNQUFNMUIsK0RBQWFBLENBQUNVLEtBQUtTLEVBQUUsRUFBRUksVUFBVUM7UUFDekQsSUFBSSxDQUFDRSxXQUFXO1lBQ2QsT0FBT3pCLGtCQUFrQndCLFdBQVcsdUNBQXVDO1FBQzdFO1FBRUEsT0FBTyxNQUFNakIsUUFBUUUsS0FBS1MsRUFBRSxFQUFFVjtJQUNoQyxFQUFFLE9BQU9FLE9BQU87UUFDZEksUUFBUUosS0FBSyxDQUFDLGNBQWNBO1FBQzVCLE9BQU9WLGtCQUNMd0IsV0FDQWQsaUJBQWlCVSxRQUFRVixNQUFNUixPQUFPLEdBQUcseUJBQ3pDO0lBRUo7QUFDRjtBQUVPLFNBQVN3QixlQUFlaEIsS0FBVTtJQUN2Q0ksUUFBUUosS0FBSyxDQUFDLGNBQWNBO0lBRTVCLElBQUlBLE9BQU9pQixTQUFTLFlBQVk7UUFDOUIsT0FBTzlCLHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7WUFBRU0sT0FBTztRQUFxQixHQUFHO1lBQUVQLFFBQVE7UUFBSTtJQUMxRTtJQUVBLElBQUlPLE9BQU9pQixTQUFTLFNBQVM7UUFDM0IsT0FBTzlCLHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7WUFBRU0sT0FBTztRQUEwQixHQUFHO1lBQUVQLFFBQVE7UUFBSTtJQUMvRTtJQUVBLE9BQU9OLHFEQUFZQSxDQUFDTyxJQUFJLENBQ3RCO1FBQUVNLE9BQU9BLE9BQU9SLFdBQVc7SUFBd0IsR0FDbkQ7UUFBRUMsUUFBUTtJQUFJO0FBRWxCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGNpcm92XFxEb2N1bWVudHNcXG5leHQtanNcXG5lY3RhclxcbmVjdGFyLW5leHRqc1xcc3JjXFxsaWJcXGFwaS11dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInXG5pbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAL2xpYi9zdXBhYmFzZS9zZXJ2ZXInXG5pbXBvcnQgeyBoYXNQZXJtaXNzaW9uLCB0eXBlIFJvbGUgfSBmcm9tICdAL2xpYi9wZXJtaXNzaW9ucydcblxuZXhwb3J0IGludGVyZmFjZSBBcGlSZXNwb25zZTxUID0gYW55PiB7XG4gIGRhdGE/OiBUXG4gIGVycm9yPzogc3RyaW5nXG4gIG1lc3NhZ2U/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUFwaVJlc3BvbnNlPFQ+KFxuICBkYXRhPzogVCxcbiAgbWVzc2FnZT86IHN0cmluZyxcbiAgc3RhdHVzOiBudW1iZXIgPSAyMDBcbik6IE5leHRSZXNwb25zZTxBcGlSZXNwb25zZTxUPj4ge1xuICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAge1xuICAgICAgZGF0YSxcbiAgICAgIG1lc3NhZ2UsXG4gICAgfSxcbiAgICB7IHN0YXR1cyB9XG4gIClcbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHdpdGhBdXRoPFQ+KFxuICByZXF1ZXN0OiBOZXh0UmVxdWVzdCxcbiAgaGFuZGxlcjogKHVzZXJJZDogc3RyaW5nLCBzdXBhYmFzZTogYW55KSA9PiBQcm9taXNlPE5leHRSZXNwb25zZTxBcGlSZXNwb25zZTxUPj4+XG4pOiBQcm9taXNlPE5leHRSZXNwb25zZTxBcGlSZXNwb25zZTxUPj4+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpXG4gICAgXG4gICAgY29uc3Qge1xuICAgICAgZGF0YTogeyB1c2VyIH0sXG4gICAgICBlcnJvcjogYXV0aEVycm9yLFxuICAgIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKVxuXG4gICAgY29uc29sZS5sb2coJ/CflJAgQXV0aCBjaGVjazonLCB7XG4gICAgICBoYXNVc2VyOiAhIXVzZXIsXG4gICAgICB1c2VySWQ6IHVzZXI/LmlkLFxuICAgICAgZW1haWw6IHVzZXI/LmVtYWlsLFxuICAgICAgZXJyb3I6IGF1dGhFcnJvcj8ubWVzc2FnZVxuICAgIH0pXG5cbiAgICBpZiAoYXV0aEVycm9yIHx8ICF1c2VyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgQXV0aCBmYWlsZWQ6JywgYXV0aEVycm9yKVxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0sIHsgc3RhdHVzOiA0MDEgfSlcbiAgICB9XG5cbiAgICByZXR1cm4gYXdhaXQgaGFuZGxlcih1c2VyLmlkLCBzdXBhYmFzZSlcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdBUEkgRXJyb3I6JywgZXJyb3IpXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB3aXRoQXV0aEFuZFBlcm1pc3Npb248VD4oXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICByZXNvdXJjZTogc3RyaW5nLFxuICBhY3Rpb246ICdjcmVhdGUnIHwgJ3JlYWQnIHwgJ3VwZGF0ZScgfCAnZGVsZXRlJyxcbiAgaGFuZGxlcjogKHVzZXJJZDogc3RyaW5nLCBzdXBhYmFzZTogYW55KSA9PiBQcm9taXNlPE5leHRSZXNwb25zZTxBcGlSZXNwb25zZTxUPj4+XG4pOiBQcm9taXNlPE5leHRSZXNwb25zZTxBcGlSZXNwb25zZTxUPj4+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpXG5cbiAgICBjb25zdCB7XG4gICAgICBkYXRhOiB7IHVzZXIgfSxcbiAgICAgIGVycm9yOiBhdXRoRXJyb3IsXG4gICAgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG5cbiAgICBpZiAoYXV0aEVycm9yIHx8ICF1c2VyKSB7XG4gICAgICByZXR1cm4gY3JlYXRlQXBpUmVzcG9uc2UodW5kZWZpbmVkLCAnVW5hdXRob3JpemVkJywgNDAxKVxuICAgIH1cblxuICAgIC8vIENoZWNrIHBlcm1pc3Npb25zXG4gICAgY29uc3QgaGFzQWNjZXNzID0gYXdhaXQgaGFzUGVybWlzc2lvbih1c2VyLmlkLCByZXNvdXJjZSwgYWN0aW9uKVxuICAgIGlmICghaGFzQWNjZXNzKSB7XG4gICAgICByZXR1cm4gY3JlYXRlQXBpUmVzcG9uc2UodW5kZWZpbmVkLCAnRm9yYmlkZGVuOiBJbnN1ZmZpY2llbnQgcGVybWlzc2lvbnMnLCA0MDMpXG4gICAgfVxuXG4gICAgcmV0dXJuIGF3YWl0IGhhbmRsZXIodXNlci5pZCwgc3VwYWJhc2UpXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignQVBJIEVycm9yOicsIGVycm9yKVxuICAgIHJldHVybiBjcmVhdGVBcGlSZXNwb25zZShcbiAgICAgIHVuZGVmaW5lZCxcbiAgICAgIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0ludGVybmFsIHNlcnZlciBlcnJvcicsXG4gICAgICA1MDBcbiAgICApXG4gIH1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGhhbmRsZUFwaUVycm9yKGVycm9yOiBhbnkpOiBOZXh0UmVzcG9uc2U8QXBpUmVzcG9uc2U+IHtcbiAgY29uc29sZS5lcnJvcignQVBJIEVycm9yOicsIGVycm9yKVxuXG4gIGlmIChlcnJvcj8uY29kZSA9PT0gJ1BHUlNUMTE2Jykge1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnUmVzb3VyY2Ugbm90IGZvdW5kJyB9LCB7IHN0YXR1czogNDA0IH0pXG4gIH1cblxuICBpZiAoZXJyb3I/LmNvZGUgPT09ICcyMzUwNScpIHtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ1Jlc291cmNlIGFscmVhZHkgZXhpc3RzJyB9LCB7IHN0YXR1czogNDA5IH0pXG4gIH1cblxuICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgeyBlcnJvcjogZXJyb3I/Lm1lc3NhZ2UgfHwgJ0ludGVybmFsIHNlcnZlciBlcnJvcicgfSxcbiAgICB7IHN0YXR1czogNTAwIH1cbiAgKVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImNyZWF0ZUNsaWVudCIsImhhc1Blcm1pc3Npb24iLCJjcmVhdGVBcGlSZXNwb25zZSIsImRhdGEiLCJtZXNzYWdlIiwic3RhdHVzIiwianNvbiIsIndpdGhBdXRoIiwicmVxdWVzdCIsImhhbmRsZXIiLCJzdXBhYmFzZSIsInVzZXIiLCJlcnJvciIsImF1dGhFcnJvciIsImF1dGgiLCJnZXRVc2VyIiwiY29uc29sZSIsImxvZyIsImhhc1VzZXIiLCJ1c2VySWQiLCJpZCIsImVtYWlsIiwiRXJyb3IiLCJ3aXRoQXV0aEFuZFBlcm1pc3Npb24iLCJyZXNvdXJjZSIsImFjdGlvbiIsInVuZGVmaW5lZCIsImhhc0FjY2VzcyIsImhhbmRsZUFwaUVycm9yIiwiY29kZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/date-utils.ts":
/*!*******************************!*\
  !*** ./src/lib/date-utils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDateBR: () => (/* binding */ formatDateBR),\n/* harmony export */   formatDateForInput: () => (/* binding */ formatDateForInput),\n/* harmony export */   formatDateTimeBR: () => (/* binding */ formatDateTimeBR),\n/* harmony export */   formatDateTimeForInput: () => (/* binding */ formatDateTimeForInput),\n/* harmony export */   formatDateWithDayBR: () => (/* binding */ formatDateWithDayBR),\n/* harmony export */   formatTimeBR: () => (/* binding */ formatTimeBR),\n/* harmony export */   getAppointmentStatusBR: () => (/* binding */ getAppointmentStatusBR),\n/* harmony export */   getAppointmentTypeBR: () => (/* binding */ getAppointmentTypeBR),\n/* harmony export */   getRelativeTimeBR: () => (/* binding */ getRelativeTimeBR),\n/* harmony export */   parseBRDate: () => (/* binding */ parseBRDate),\n/* harmony export */   toLocalISOString: () => (/* binding */ toLocalISOString)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(rsc)/./node_modules/date-fns/parseISO.js\");\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(rsc)/./node_modules/date-fns/isValid.js\");\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(rsc)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! date-fns/locale */ \"(rsc)/./node_modules/date-fns/locale/pt-BR.js\");\n\n\n/**\n * Utility functions for Brazilian date and time formatting\n */ /**\n * Format date to Brazilian format (DD/MM/YYYY)\n */ function formatDateBR(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(dateObj, 'dd/MM/yyyy', {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.ptBR\n        });\n    } catch  {\n        return '';\n    }\n}\n/**\n * Format time to Brazilian format (HH:mm)\n */ function formatTimeBR(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(dateObj, 'HH:mm', {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.ptBR\n        });\n    } catch  {\n        return '';\n    }\n}\n/**\n * Format date and time to Brazilian format (DD/MM/YYYY HH:mm)\n */ function formatDateTimeBR(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(dateObj, 'dd/MM/yyyy HH:mm', {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.ptBR\n        });\n    } catch  {\n        return '';\n    }\n}\n/**\n * Format date to Brazilian format with day of week (Segunda, DD/MM/YYYY)\n */ function formatDateWithDayBR(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(dateObj, 'EEEE, dd/MM/yyyy', {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.ptBR\n        });\n    } catch  {\n        return '';\n    }\n}\n/**\n * Format date for input fields (YYYY-MM-DD)\n */ function formatDateForInput(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(dateObj, 'yyyy-MM-dd');\n    } catch  {\n        return '';\n    }\n}\n/**\n * Format datetime for input fields (YYYY-MM-DDTHH:mm)\n * This function handles Brazilian timezone properly to avoid UTC conversion issues\n */ function formatDateTimeForInput(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        // Create a new date in local timezone to avoid UTC conversion\n        const localDate = new Date(dateObj.getTime() - dateObj.getTimezoneOffset() * 60000);\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(localDate, \"yyyy-MM-dd'T'HH:mm\");\n    } catch  {\n        return '';\n    }\n}\n/**\n * Convert local datetime to ISO string without timezone conversion\n * This prevents the 3-hour offset issue in Brazilian timezone\n */ function toLocalISOString(date) {\n    try {\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(date)) return '';\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        const hours = String(date.getHours()).padStart(2, '0');\n        const minutes = String(date.getMinutes()).padStart(2, '0');\n        return `${year}-${month}-${day}T${hours}:${minutes}`;\n    } catch  {\n        return '';\n    }\n}\n/**\n * Get relative time in Portuguese (hoje, ontem, amanhã, etc.)\n */ function getRelativeTimeBR(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        const today = new Date();\n        const tomorrow = new Date(today);\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const dateStr = (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(dateObj, 'yyyy-MM-dd');\n        const todayStr = (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(today, 'yyyy-MM-dd');\n        const tomorrowStr = (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(tomorrow, 'yyyy-MM-dd');\n        const yesterdayStr = (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(yesterday, 'yyyy-MM-dd');\n        if (dateStr === todayStr) return 'Hoje';\n        if (dateStr === tomorrowStr) return 'Amanhã';\n        if (dateStr === yesterdayStr) return 'Ontem';\n        return formatDateBR(dateObj);\n    } catch  {\n        return '';\n    }\n}\n/**\n * Parse Brazilian date format (DD/MM/YYYY) to Date object\n */ function parseBRDate(dateStr) {\n    try {\n        const parts = dateStr.split('/');\n        if (parts.length !== 3) return null;\n        const day = parseInt(parts[0], 10);\n        const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed\n        const year = parseInt(parts[2], 10);\n        const date = new Date(year, month, day);\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(date)) return null;\n        return date;\n    } catch  {\n        return null;\n    }\n}\n/**\n * Get appointment status in Portuguese\n */ function getAppointmentStatusBR(status) {\n    const statusMap = {\n        'scheduled': 'Agendado',\n        'confirmed': 'Confirmado',\n        'in_progress': 'Em Andamento',\n        'completed': 'Concluído',\n        'cancelled': 'Cancelado',\n        'no_show': 'Faltou'\n    };\n    return statusMap[status] || status;\n}\n/**\n * Get appointment type in Portuguese\n */ function getAppointmentTypeBR(type) {\n    const typeMap = {\n        'consultation': 'Consulta',\n        'return': 'Retorno',\n        'teleconsultation': 'Teleconsulta',\n        'procedure': 'Procedimento',\n        'exam': 'Exame'\n    };\n    return typeMap[type] || type;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/date-utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/permissions.ts":
/*!********************************!*\
  !*** ./src/lib/permissions.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_PERMISSIONS: () => (/* binding */ DEFAULT_PERMISSIONS),\n/* harmony export */   getUserPermissions: () => (/* binding */ getUserPermissions),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hasRole: () => (/* binding */ hasRole),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   requirePermission: () => (/* binding */ requirePermission),\n/* harmony export */   requireRole: () => (/* binding */ requireRole)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://zmwdnemlzndjavlriyrc.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Create a Supabase client with service role key for server-side operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n/**\n * Check if a user has a specific permission\n */ async function hasPermission(userId, resource, action) {\n    try {\n        // Get user roles\n        const { data: userRoles, error: rolesError } = await supabaseAdmin.from('user_roles').select('role_name').eq('user_id', userId);\n        if (rolesError || !userRoles || userRoles.length === 0) {\n            return false;\n        }\n        // Check if any of the user's roles have the required permission\n        const roleNames = userRoles.map((role)=>role.role_name);\n        const { data: permissions, error: permissionsError } = await supabaseAdmin.from('permissions').select('*').in('role_name', roleNames).eq('resource', resource).eq('action', action);\n        if (permissionsError) {\n            console.error('Error checking permissions:', permissionsError);\n            return false;\n        }\n        return permissions && permissions.length > 0;\n    } catch (error) {\n        console.error('Error in hasPermission:', error);\n        return false;\n    }\n}\n/**\n * Check if a user has any of the specified roles\n */ async function hasRole(userId, roles) {\n    try {\n        const { data: userRoles, error } = await supabaseAdmin.from('user_roles').select('role_name').eq('user_id', userId).in('role_name', roles);\n        if (error) {\n            console.error('Error checking roles:', error);\n            return false;\n        }\n        return userRoles && userRoles.length > 0;\n    } catch (error) {\n        console.error('Error in hasRole:', error);\n        return false;\n    }\n}\n/**\n * Get all permissions for a user\n */ async function getUserPermissions(userId) {\n    try {\n        // Get user roles\n        const { data: userRoles, error: rolesError } = await supabaseAdmin.from('user_roles').select('role_name').eq('user_id', userId);\n        if (rolesError || !userRoles || userRoles.length === 0) {\n            return [];\n        }\n        // Get all permissions for these roles\n        const roleNames = userRoles.map((role)=>role.role_name);\n        const { data: permissions, error: permissionsError } = await supabaseAdmin.from('permissions').select('resource, action').in('role_name', roleNames);\n        if (permissionsError) {\n            console.error('Error getting user permissions:', permissionsError);\n            return [];\n        }\n        return permissions || [];\n    } catch (error) {\n        console.error('Error in getUserPermissions:', error);\n        return [];\n    }\n}\n/**\n * Check if a user is an admin\n */ async function isAdmin(userId) {\n    return hasRole(userId, [\n        'admin'\n    ]);\n}\n/**\n * Middleware function to check permissions for API routes\n */ function requirePermission(resource, action) {\n    return async (userId)=>{\n        return hasPermission(userId, resource, action);\n    };\n}\n/**\n * Middleware function to check roles for API routes\n */ function requireRole(roles) {\n    return async (userId)=>{\n        return hasRole(userId, roles);\n    };\n}\n/**\n * Default permissions for each role\n */ const DEFAULT_PERMISSIONS = {\n    admin: [\n        // Full access to everything\n        {\n            resource: 'appointments',\n            action: 'create'\n        },\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'appointments',\n            action: 'update'\n        },\n        {\n            resource: 'appointments',\n            action: 'delete'\n        },\n        {\n            resource: 'patients',\n            action: 'create'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'update'\n        },\n        {\n            resource: 'patients',\n            action: 'delete'\n        },\n        {\n            resource: 'medical_records',\n            action: 'create'\n        },\n        {\n            resource: 'medical_records',\n            action: 'read'\n        },\n        {\n            resource: 'medical_records',\n            action: 'update'\n        },\n        {\n            resource: 'medical_records',\n            action: 'delete'\n        },\n        {\n            resource: 'procedures',\n            action: 'create'\n        },\n        {\n            resource: 'procedures',\n            action: 'read'\n        },\n        {\n            resource: 'procedures',\n            action: 'update'\n        },\n        {\n            resource: 'procedures',\n            action: 'delete'\n        },\n        {\n            resource: 'settings',\n            action: 'read'\n        },\n        {\n            resource: 'settings',\n            action: 'update'\n        }\n    ],\n    doctor: [\n        // Can manage appointments and patients, read procedures\n        {\n            resource: 'appointments',\n            action: 'create'\n        },\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'appointments',\n            action: 'update'\n        },\n        {\n            resource: 'patients',\n            action: 'create'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'update'\n        },\n        {\n            resource: 'medical_records',\n            action: 'create'\n        },\n        {\n            resource: 'medical_records',\n            action: 'read'\n        },\n        {\n            resource: 'medical_records',\n            action: 'update'\n        },\n        {\n            resource: 'procedures',\n            action: 'read'\n        }\n    ],\n    secretary: [\n        // Can manage appointments and patients, read procedures\n        {\n            resource: 'appointments',\n            action: 'create'\n        },\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'appointments',\n            action: 'update'\n        },\n        {\n            resource: 'patients',\n            action: 'create'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'update'\n        },\n        {\n            resource: 'procedures',\n            action: 'read'\n        }\n    ],\n    assistant: [\n        // Read-only access to appointments and patients\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/permissions.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://zmwdnemlzndjavlriyrc.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inptd2RuZW1sem5kamF2bHJpeXJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MTE5NTksImV4cCI6MjA2NzM4Nzk1OX0.XNRQjZmMZ7s4aKrJVSQFlu9ASryGJc5fBX6iNnjOPEM\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/date-fns"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();