-- Migration 005: Update RLS Policies for Multi-User Access
-- Updates all RLS policies to support cross-user access based on associations

-- 1. Update appointments policies
DROP POLICY IF EXISTS "Users can manage own appointments" ON appointments;
DROP POLICY IF EXISTS "Users can view own appointments" ON appointments;
DROP POLICY IF EXISTS "Users can create own appointments" ON appointments;
DROP POLICY IF EXISTS "Users can update own appointments" ON appointments;
DROP POLICY IF EXISTS "Users can delete own appointments" ON appointments;

CREATE POLICY "Users can view accessible appointments" ON appointments
  FOR SELECT USING (has_access_to_user(user_id, 'appointments', 'read'));

CREATE POLICY "Users can create accessible appointments" ON appointments
  FOR INSERT WITH CHECK (has_access_to_user(user_id, 'appointments', 'create'));

CREATE POLICY "Users can update accessible appointments" ON appointments
  FOR UPDATE USING (has_access_to_user(user_id, 'appointments', 'update'));

CREATE POLICY "Users can delete accessible appointments" ON appointments
  FOR DELETE USING (has_access_to_user(user_id, 'appointments', 'delete'));

-- 2. Update patients policies
DROP POLICY IF EXISTS "Users can manage own patients" ON patients;
DROP POLICY IF EXISTS "Users can view own patients" ON patients;
DROP POLICY IF EXISTS "Users can create own patients" ON patients;
DROP POLICY IF EXISTS "Users can update own patients" ON patients;
DROP POLICY IF EXISTS "Users can delete own patients" ON patients;

CREATE POLICY "Users can view accessible patients" ON patients
  FOR SELECT USING (has_access_to_user(user_id, 'patients', 'read'));

CREATE POLICY "Users can create accessible patients" ON patients
  FOR INSERT WITH CHECK (has_access_to_user(user_id, 'patients', 'create'));

CREATE POLICY "Users can update accessible patients" ON patients
  FOR UPDATE USING (has_access_to_user(user_id, 'patients', 'update'));

CREATE POLICY "Users can delete accessible patients" ON patients
  FOR DELETE USING (has_access_to_user(user_id, 'patients', 'delete'));

-- 3. Update medical_records policies
DROP POLICY IF EXISTS "Users can manage own medical records" ON medical_records;
DROP POLICY IF EXISTS "Users can view own medical records" ON medical_records;
DROP POLICY IF EXISTS "Users can create own medical records" ON medical_records;
DROP POLICY IF EXISTS "Users can update own medical records" ON medical_records;
DROP POLICY IF EXISTS "Users can delete own medical records" ON medical_records;

CREATE POLICY "Users can view accessible medical records" ON medical_records
  FOR SELECT USING (has_access_to_user(user_id, 'medical_records', 'read'));

CREATE POLICY "Users can create accessible medical records" ON medical_records
  FOR INSERT WITH CHECK (has_access_to_user(user_id, 'medical_records', 'create'));

CREATE POLICY "Users can update accessible medical records" ON medical_records
  FOR UPDATE USING (has_access_to_user(user_id, 'medical_records', 'update'));

CREATE POLICY "Users can delete accessible medical records" ON medical_records
  FOR DELETE USING (has_access_to_user(user_id, 'medical_records', 'delete'));

-- 4. Update procedures policies
DROP POLICY IF EXISTS "Users can manage own procedures" ON procedures;
DROP POLICY IF EXISTS "Users can view own procedures" ON procedures;
DROP POLICY IF EXISTS "Users can create own procedures" ON procedures;
DROP POLICY IF EXISTS "Users can update own procedures" ON procedures;
DROP POLICY IF EXISTS "Users can delete own procedures" ON procedures;

CREATE POLICY "Users can view accessible procedures" ON procedures
  FOR SELECT USING (has_access_to_user(user_id, 'procedures', 'read'));

CREATE POLICY "Users can create accessible procedures" ON procedures
  FOR INSERT WITH CHECK (has_access_to_user(user_id, 'procedures', 'create'));

CREATE POLICY "Users can update accessible procedures" ON procedures
  FOR UPDATE USING (has_access_to_user(user_id, 'procedures', 'update'));

CREATE POLICY "Users can delete accessible procedures" ON procedures
  FOR DELETE USING (has_access_to_user(user_id, 'procedures', 'delete'));

-- 5. Update patient_attachments policies
DROP POLICY IF EXISTS "Users can manage own patient attachments" ON patient_attachments;
DROP POLICY IF EXISTS "Users can view own patient attachments" ON patient_attachments;
DROP POLICY IF EXISTS "Users can create own patient attachments" ON patient_attachments;
DROP POLICY IF EXISTS "Users can update own patient attachments" ON patient_attachments;
DROP POLICY IF EXISTS "Users can delete own patient attachments" ON patient_attachments;

CREATE POLICY "Users can view accessible patient attachments" ON patient_attachments
  FOR SELECT USING (has_access_to_user(user_id, 'patient_attachments', 'read'));

CREATE POLICY "Users can create accessible patient attachments" ON patient_attachments
  FOR INSERT WITH CHECK (has_access_to_user(user_id, 'patient_attachments', 'create'));

CREATE POLICY "Users can update accessible patient attachments" ON patient_attachments
  FOR UPDATE USING (has_access_to_user(user_id, 'patient_attachments', 'update'));

CREATE POLICY "Users can delete accessible patient attachments" ON patient_attachments
  FOR DELETE USING (has_access_to_user(user_id, 'patient_attachments', 'delete'));

-- 6. Update healthcare_professionals policies
DROP POLICY IF EXISTS "Users can manage own healthcare professionals" ON healthcare_professionals;
DROP POLICY IF EXISTS "Users can view own healthcare professionals" ON healthcare_professionals;
DROP POLICY IF EXISTS "Users can create own healthcare professionals" ON healthcare_professionals;
DROP POLICY IF EXISTS "Users can update own healthcare professionals" ON healthcare_professionals;
DROP POLICY IF EXISTS "Users can delete own healthcare professionals" ON healthcare_professionals;

CREATE POLICY "Users can view accessible healthcare professionals" ON healthcare_professionals
  FOR SELECT USING (has_access_to_user(user_id, 'healthcare_professionals', 'read'));

CREATE POLICY "Users can create accessible healthcare professionals" ON healthcare_professionals
  FOR INSERT WITH CHECK (has_access_to_user(user_id, 'healthcare_professionals', 'create'));

CREATE POLICY "Users can update accessible healthcare professionals" ON healthcare_professionals
  FOR UPDATE USING (has_access_to_user(user_id, 'healthcare_professionals', 'update'));

CREATE POLICY "Users can delete accessible healthcare professionals" ON healthcare_professionals
  FOR DELETE USING (has_access_to_user(user_id, 'healthcare_professionals', 'delete'));

-- 7. Update appointment_procedures policies (if exists)
DROP POLICY IF EXISTS "Users can manage own appointment procedures" ON appointment_procedures;
DROP POLICY IF EXISTS "Users can view own appointment procedures" ON appointment_procedures;
DROP POLICY IF EXISTS "Users can create own appointment procedures" ON appointment_procedures;
DROP POLICY IF EXISTS "Users can update own appointment procedures" ON appointment_procedures;
DROP POLICY IF EXISTS "Users can delete own appointment procedures" ON appointment_procedures;

-- For appointment_procedures, we need to check access through the appointment
CREATE POLICY "Users can view accessible appointment procedures" ON appointment_procedures
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM appointments a 
      WHERE a.id = appointment_procedures.appointment_id 
      AND has_access_to_user(a.user_id, 'appointments', 'read')
    )
  );

CREATE POLICY "Users can create accessible appointment procedures" ON appointment_procedures
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM appointments a 
      WHERE a.id = appointment_procedures.appointment_id 
      AND has_access_to_user(a.user_id, 'appointments', 'update')
    )
  );

CREATE POLICY "Users can update accessible appointment procedures" ON appointment_procedures
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM appointments a 
      WHERE a.id = appointment_procedures.appointment_id 
      AND has_access_to_user(a.user_id, 'appointments', 'update')
    )
  );

CREATE POLICY "Users can delete accessible appointment procedures" ON appointment_procedures
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM appointments a 
      WHERE a.id = appointment_procedures.appointment_id 
      AND has_access_to_user(a.user_id, 'appointments', 'update')
    )
  );

-- 8. Update consultation_attachments policies (if exists)
DROP POLICY IF EXISTS "Users can view their own consultation attachments" ON consultation_attachments;
DROP POLICY IF EXISTS "Users can insert their own consultation attachments" ON consultation_attachments;
DROP POLICY IF EXISTS "Users can update their own consultation attachments" ON consultation_attachments;
DROP POLICY IF EXISTS "Users can delete their own consultation attachments" ON consultation_attachments;

-- For consultation_attachments, check access through the appointment
CREATE POLICY "Users can view accessible consultation attachments" ON consultation_attachments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM appointments a 
      WHERE a.id = consultation_attachments.appointment_id 
      AND has_access_to_user(a.user_id, 'appointments', 'read')
    )
  );

CREATE POLICY "Users can create accessible consultation attachments" ON consultation_attachments
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM appointments a 
      WHERE a.id = consultation_attachments.appointment_id 
      AND has_access_to_user(a.user_id, 'appointments', 'update')
    )
  );

CREATE POLICY "Users can update accessible consultation attachments" ON consultation_attachments
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM appointments a 
      WHERE a.id = consultation_attachments.appointment_id 
      AND has_access_to_user(a.user_id, 'appointments', 'update')
    )
  );

CREATE POLICY "Users can delete accessible consultation attachments" ON consultation_attachments
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM appointments a 
      WHERE a.id = consultation_attachments.appointment_id 
      AND has_access_to_user(a.user_id, 'appointments', 'update')
    )
  );

-- 9. Update clinic_settings policies
DROP POLICY IF EXISTS "Users can manage own clinic settings" ON clinic_settings;
DROP POLICY IF EXISTS "Users can view own clinic settings" ON clinic_settings;
DROP POLICY IF EXISTS "Users can create own clinic settings" ON clinic_settings;
DROP POLICY IF EXISTS "Users can update own clinic settings" ON clinic_settings;
DROP POLICY IF EXISTS "Users can delete own clinic settings" ON clinic_settings;

CREATE POLICY "Users can view accessible clinic settings" ON clinic_settings
  FOR SELECT USING (has_access_to_user(user_id, 'clinic_settings', 'read'));

CREATE POLICY "Users can create accessible clinic settings" ON clinic_settings
  FOR INSERT WITH CHECK (has_access_to_user(user_id, 'clinic_settings', 'create'));

CREATE POLICY "Users can update accessible clinic settings" ON clinic_settings
  FOR UPDATE USING (has_access_to_user(user_id, 'clinic_settings', 'update'));

CREATE POLICY "Users can delete accessible clinic settings" ON clinic_settings
  FOR DELETE USING (has_access_to_user(user_id, 'clinic_settings', 'delete'));
