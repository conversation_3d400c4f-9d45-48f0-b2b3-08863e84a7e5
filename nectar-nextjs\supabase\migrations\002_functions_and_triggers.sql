-- Function to automatically create user profile when auth.users record is created
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, name)
  VALUES (NEW.id, NEW.email, COALESCE(NEW.raw_user_meta_data->>'name', NEW.email));
  
  -- Assign default role
  INSERT INTO public.user_roles (user_id, role_name)
  VALUES (NEW.id, 'admin'); -- First user gets admin role, you can modify this logic
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to call handle_new_user function
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers to all tables that have updated_at column
CREATE TRIGGER handle_updated_at_users
  BEFORE UPDATE ON public.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_patients
  BEFORE UPDATE ON public.patients
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_healthcare_professionals
  BEFORE UPDATE ON public.healthcare_professionals
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_procedures
  BEFORE UPDATE ON public.procedures
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_appointments
  BEFORE UPDATE ON public.appointments
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_clinic_settings
  BEFORE UPDATE ON public.clinic_settings
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Function to get user permissions
CREATE OR REPLACE FUNCTION public.get_user_permissions(user_uuid UUID)
RETURNS TABLE(resource TEXT, action TEXT) AS $$
BEGIN
  RETURN QUERY
  SELECT p.resource, p.action
  FROM public.permissions p
  INNER JOIN public.user_roles ur ON ur.role_name = p.role_name
  WHERE ur.user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has specific permission
CREATE OR REPLACE FUNCTION public.user_has_permission(
  user_uuid UUID,
  resource_name TEXT,
  action_name TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.permissions p
    INNER JOIN public.user_roles ur ON ur.role_name = p.role_name
    WHERE ur.user_id = user_uuid
    AND p.resource = resource_name
    AND p.action = action_name
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get appointment counts by date
CREATE OR REPLACE FUNCTION public.get_appointment_counts_by_date(
  user_uuid UUID,
  start_date DATE,
  end_date DATE
)
RETURNS TABLE(appointment_date DATE, count BIGINT) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    DATE(a.start_time) as appointment_date,
    COUNT(*) as count
  FROM public.appointments a
  WHERE a.user_id = user_uuid
  AND DATE(a.start_time) BETWEEN start_date AND end_date
  AND a.status != 'cancelled'
  GROUP BY DATE(a.start_time)
  ORDER BY appointment_date;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get appointments with patient and professional details
CREATE OR REPLACE FUNCTION public.get_appointments_with_details(
  user_uuid UUID,
  filter_date DATE DEFAULT NULL,
  filter_patient_id UUID DEFAULT NULL,
  filter_professional_id UUID DEFAULT NULL
)
RETURNS TABLE(
  id UUID,
  title TEXT,
  description TEXT,
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  type TEXT,
  status TEXT,
  price DECIMAL,
  notes TEXT,
  patient_id UUID,
  patient_name TEXT,
  healthcare_professional_id UUID,
  healthcare_professional_name TEXT,
  healthcare_professional_specialty TEXT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id,
    a.title,
    a.description,
    a.start_time,
    a.end_time,
    a.type,
    a.status,
    a.price,
    a.notes,
    a.patient_id,
    p.name as patient_name,
    a.healthcare_professional_id,
    hp.name as healthcare_professional_name,
    hp.specialty as healthcare_professional_specialty,
    a.created_at
  FROM public.appointments a
  INNER JOIN public.patients p ON p.id = a.patient_id
  LEFT JOIN public.healthcare_professionals hp ON hp.id = a.healthcare_professional_id
  WHERE a.user_id = user_uuid
  AND (filter_date IS NULL OR DATE(a.start_time) = filter_date)
  AND (filter_patient_id IS NULL OR a.patient_id = filter_patient_id)
  AND (filter_professional_id IS NULL OR a.healthcare_professional_id = filter_professional_id)
  ORDER BY a.start_time;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate appointment total with procedures
CREATE OR REPLACE FUNCTION public.calculate_appointment_total(appointment_uuid UUID)
RETURNS DECIMAL AS $$
DECLARE
  total DECIMAL := 0;
BEGIN
  SELECT COALESCE(SUM(ap.total_price), 0)
  INTO total
  FROM public.appointment_procedures ap
  WHERE ap.appointment_id = appointment_uuid;
  
  RETURN total;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update appointment total when procedures change
CREATE OR REPLACE FUNCTION public.update_appointment_total()
RETURNS TRIGGER AS $$
DECLARE
  appointment_uuid UUID;
  new_total DECIMAL;
BEGIN
  -- Get appointment ID from either NEW or OLD record
  appointment_uuid := COALESCE(NEW.appointment_id, OLD.appointment_id);
  
  -- Calculate new total
  SELECT public.calculate_appointment_total(appointment_uuid) INTO new_total;
  
  -- Update appointment price
  UPDATE public.appointments 
  SET price = new_total 
  WHERE id = appointment_uuid;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update appointment total when procedures are modified
CREATE TRIGGER update_appointment_total_on_procedures
  AFTER INSERT OR UPDATE OR DELETE ON public.appointment_procedures
  FOR EACH ROW EXECUTE FUNCTION public.update_appointment_total();

-- Create storage bucket for patient attachments
INSERT INTO storage.buckets (id, name, public) 
VALUES ('patient-attachments', 'patient-attachments', false)
ON CONFLICT (id) DO NOTHING;

-- Create storage policy for patient attachments
CREATE POLICY "Users can upload patient attachments" ON storage.objects
  FOR INSERT TO authenticated WITH CHECK (
    bucket_id = 'patient-attachments' AND
    EXISTS (
      SELECT 1 FROM public.patients p
      WHERE p.user_id = auth.uid()
      AND (storage.foldername(name))[1] = p.id::text
    )
  );

CREATE POLICY "Users can view own patient attachments" ON storage.objects
  FOR SELECT TO authenticated USING (
    bucket_id = 'patient-attachments' AND
    EXISTS (
      SELECT 1 FROM public.patients p
      WHERE p.user_id = auth.uid()
      AND (storage.foldername(name))[1] = p.id::text
    )
  );

CREATE POLICY "Users can delete own patient attachments" ON storage.objects
  FOR DELETE TO authenticated USING (
    bucket_id = 'patient-attachments' AND
    EXISTS (
      SELECT 1 FROM public.patients p
      WHERE p.user_id = auth.uid()
      AND (storage.foldername(name))[1] = p.id::text
    )
  );
