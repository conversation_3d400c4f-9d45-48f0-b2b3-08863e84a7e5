-- Migration 003: User System Fixes and Auto-population
-- Fixes critical bugs in user creation and adds auto-population triggers

-- 1. Create function to sync auth.users to public.users
CREATE OR REPLACE FUNCTION sync_user_to_public()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert or update user in public.users when auth.users changes
  INSERT INTO public.users (
    id,
    email,
    name,
    phone,
    role,
    is_active,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', NEW.email),
    NEW.raw_user_meta_data->>'phone',
    COALESCE(NEW.raw_user_meta_data->>'role', 'user'),
    true,
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    name = COALESCE(EXCLUDED.name, users.name),
    phone = COALESCE(EXCLUDED.phone, users.phone),
    updated_at = NOW();

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Create trigger for auth.users sync
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT OR UPDATE ON auth.users
  FOR EACH ROW EXECUTE FUNCTION sync_user_to_public();

-- 3. Create function to auto-populate role-specific tables
CREATE OR REPLACE FUNCTION auto_populate_role_tables()
RETURNS TRIGGER AS $$
BEGIN
  -- Auto-populate healthcare_professionals for doctors
  IF NEW.role = 'doctor' THEN
    INSERT INTO healthcare_professionals (
      user_id,
      name,
      email,
      phone,
      specialty,
      is_active,
      created_at,
      updated_at
    )
    VALUES (
      NEW.id,
      NEW.name,
      NEW.email,
      NEW.phone,
      'Clínico Geral', -- Default specialty
      true,
      NOW(),
      NOW()
    )
    ON CONFLICT (user_id) DO NOTHING;
  END IF;

  -- Create user_role entry
  INSERT INTO user_roles (
    user_id,
    role_name,
    created_at
  )
  VALUES (
    NEW.id,
    NEW.role,
    NOW()
  )
  ON CONFLICT (user_id, role_name) DO NOTHING;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Create trigger for auto-population
DROP TRIGGER IF EXISTS on_user_role_populate ON public.users;
CREATE TRIGGER on_user_role_populate
  AFTER INSERT OR UPDATE OF role ON public.users
  FOR EACH ROW EXECUTE FUNCTION auto_populate_role_tables();

-- 5. Add unique constraint to healthcare_professionals.user_id if not exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'healthcare_professionals_user_id_key'
  ) THEN
    ALTER TABLE healthcare_professionals 
    ADD CONSTRAINT healthcare_professionals_user_id_key UNIQUE (user_id);
  END IF;
END $$;

-- 6. Add unique constraint to user_roles (user_id, role_name) if not exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'user_roles_user_id_role_name_key'
  ) THEN
    ALTER TABLE user_roles 
    ADD CONSTRAINT user_roles_user_id_role_name_key UNIQUE (user_id, role_name);
  END IF;
END $$;

-- 7. Create secretaries table
CREATE TABLE IF NOT EXISTS secretaries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  department TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT secretaries_user_id_key UNIQUE (user_id)
);

-- 8. Create indexes for secretaries table
CREATE INDEX IF NOT EXISTS idx_secretaries_user_id ON secretaries(user_id);
CREATE INDEX IF NOT EXISTS idx_secretaries_is_active ON secretaries(is_active);

-- 9. Enable RLS on secretaries table
ALTER TABLE secretaries ENABLE ROW LEVEL SECURITY;

-- 10. Create RLS policies for secretaries
CREATE POLICY "Users can view own secretary profile" ON secretaries
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update own secretary profile" ON secretaries
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Admins can manage all secretaries" ON secretaries
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() AND role_name = 'admin'
    )
  );

-- 11. Create trigger for secretaries updated_at
CREATE OR REPLACE FUNCTION update_secretaries_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_secretaries_updated_at
  BEFORE UPDATE ON secretaries
  FOR EACH ROW
  EXECUTE FUNCTION update_secretaries_updated_at();

-- 12. Update auto_populate_role_tables to include secretaries
CREATE OR REPLACE FUNCTION auto_populate_role_tables()
RETURNS TRIGGER AS $$
BEGIN
  -- Auto-populate healthcare_professionals for doctors
  IF NEW.role = 'doctor' THEN
    INSERT INTO healthcare_professionals (
      user_id,
      name,
      email,
      phone,
      specialty,
      is_active,
      created_at,
      updated_at
    )
    VALUES (
      NEW.id,
      NEW.name,
      NEW.email,
      NEW.phone,
      'Clínico Geral', -- Default specialty
      true,
      NOW(),
      NOW()
    )
    ON CONFLICT (user_id) DO NOTHING;
  END IF;

  -- Auto-populate secretaries for secretary role
  IF NEW.role = 'secretary' THEN
    INSERT INTO secretaries (
      user_id,
      name,
      email,
      phone,
      department,
      is_active,
      created_at,
      updated_at
    )
    VALUES (
      NEW.id,
      NEW.name,
      NEW.email,
      NEW.phone,
      'Administração', -- Default department
      true,
      NOW(),
      NOW()
    )
    ON CONFLICT (user_id) DO NOTHING;
  END IF;

  -- Create user_role entry
  INSERT INTO user_roles (
    user_id,
    role_name,
    created_at
  )
  VALUES (
    NEW.id,
    NEW.role,
    NOW()
  )
  ON CONFLICT (user_id, role_name) DO NOTHING;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
