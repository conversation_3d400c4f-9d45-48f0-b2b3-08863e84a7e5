-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom users table to extend auth.users
CREATE TABLE IF NOT EXISTS public.users (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  phone TEXT,
  avatar_url TEXT,
  role TEXT DEFAULT 'user',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for users table
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Create patients table
CREATE TABLE IF NOT EXISTS public.patients (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  birth_date DATE,
  cpf TEXT,
  address TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on patients table
ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for patients table
CREATE POLICY "Users can manage own patients" ON public.patients
  FOR ALL USING (auth.uid() = user_id);

-- Create healthcare_professionals table
CREATE TABLE IF NOT EXISTS public.healthcare_professionals (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  specialty TEXT,
  crm TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on healthcare_professionals table
ALTER TABLE public.healthcare_professionals ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for healthcare_professionals table
CREATE POLICY "Users can manage own healthcare professionals" ON public.healthcare_professionals
  FOR ALL USING (auth.uid() = user_id);

-- Create procedures table
CREATE TABLE IF NOT EXISTS public.procedures (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  default_price DECIMAL(10,2),
  duration_minutes INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on procedures table
ALTER TABLE public.procedures ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for procedures table
CREATE POLICY "Users can manage own procedures" ON public.procedures
  FOR ALL USING (auth.uid() = user_id);

-- Create appointments table
CREATE TABLE IF NOT EXISTS public.appointments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  patient_id UUID REFERENCES public.patients(id) ON DELETE CASCADE NOT NULL,
  healthcare_professional_id UUID REFERENCES public.healthcare_professionals(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  description TEXT,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  type TEXT DEFAULT 'consultation',
  status TEXT DEFAULT 'scheduled',
  price DECIMAL(10,2),
  notes TEXT,
  recurrence_rule TEXT,
  recurrence_end_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT valid_appointment_times CHECK (end_time > start_time)
);

-- Enable RLS on appointments table
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for appointments table
CREATE POLICY "Users can manage own appointments" ON public.appointments
  FOR ALL USING (auth.uid() = user_id);

-- Create appointment_procedures table
CREATE TABLE IF NOT EXISTS public.appointment_procedures (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  appointment_id UUID REFERENCES public.appointments(id) ON DELETE CASCADE NOT NULL,
  procedure_id UUID REFERENCES public.procedures(id) ON DELETE CASCADE NOT NULL,
  quantity INTEGER DEFAULT 1,
  unit_price DECIMAL(10,2) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on appointment_procedures table
ALTER TABLE public.appointment_procedures ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for appointment_procedures table
CREATE POLICY "Users can manage appointment procedures through appointments" ON public.appointment_procedures
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.appointments 
      WHERE appointments.id = appointment_procedures.appointment_id 
      AND appointments.user_id = auth.uid()
    )
  );

-- Create user_roles table
CREATE TABLE IF NOT EXISTS public.user_roles (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  role_name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, role_name)
);

-- Enable RLS on user_roles table
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_roles table
CREATE POLICY "Users can view own roles" ON public.user_roles
  FOR SELECT USING (auth.uid() = user_id);

-- Create permissions table
CREATE TABLE IF NOT EXISTS public.permissions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  role_name TEXT NOT NULL,
  resource TEXT NOT NULL,
  action TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(role_name, resource, action)
);

-- Enable RLS on permissions table
ALTER TABLE public.permissions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for permissions table
CREATE POLICY "All authenticated users can view permissions" ON public.permissions
  FOR SELECT TO authenticated USING (true);

-- Create clinic_settings table
CREATE TABLE IF NOT EXISTS public.clinic_settings (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  setting_key TEXT NOT NULL,
  setting_value JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, setting_key)
);

-- Enable RLS on clinic_settings table
ALTER TABLE public.clinic_settings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for clinic_settings table
CREATE POLICY "Users can manage own clinic settings" ON public.clinic_settings
  FOR ALL USING (auth.uid() = user_id);

-- Create patient_attachments table
CREATE TABLE IF NOT EXISTS public.patient_attachments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  patient_id UUID REFERENCES public.patients(id) ON DELETE CASCADE NOT NULL,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT,
  uploaded_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on patient_attachments table
ALTER TABLE public.patient_attachments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for patient_attachments table
CREATE POLICY "Users can manage attachments for own patients" ON public.patient_attachments
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.patients 
      WHERE patients.id = patient_attachments.patient_id 
      AND patients.user_id = auth.uid()
    )
  );
