"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { 
  Users, 
  UserPlus, 
  Trash2, 
  Settings, 
  Eye,
  AlertCircle,
  Link,
  Unlink
} from 'lucide-react'
import { makeAuthenticatedRequest } from '@/lib/api-client'
import { formatDateTimeBR } from '@/lib/date-utils'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

interface User {
  id: string
  email: string
  name: string
  role: string
  is_active: boolean
}

interface UserAssociation {
  id: string
  accessor_user_id: string
  target_user_id: string
  association_type: 'secretary_to_doctor' | 'doctor_to_doctor'
  permissions?: Record<string, string[]> | null
  is_active: boolean
  created_by: string
  created_at: string
  accessor_user: User
  target_user: User
  created_by_user: User
}

interface CreateAssociationForm {
  accessor_user_id: string
  target_user_id: string
  association_type: 'secretary_to_doctor' | 'doctor_to_doctor'
  permissions: Record<string, string[]>
}

const AVAILABLE_PERMISSIONS = {
  appointments: ['read', 'create', 'update', 'delete'],
  patients: ['read', 'create', 'update', 'delete'],
  medical_records: ['read', 'create', 'update', 'delete'],
  procedures: ['read', 'create', 'update', 'delete'],
  healthcare_professionals: ['read', 'create', 'update', 'delete']
}

export function UserAssociationsManager() {
  const [associations, setAssociations] = useState<UserAssociation[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedAssociation, setSelectedAssociation] = useState<UserAssociation | null>(null)
  const { toast } = useToast()

  const [form, setForm] = useState<CreateAssociationForm>({
    accessor_user_id: '',
    target_user_id: '',
    association_type: 'secretary_to_doctor',
    permissions: {}
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      await Promise.all([
        fetchAssociations(),
        fetchUsers()
      ])
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: "Erro",
        description: "Erro ao carregar dados das associações.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchAssociations = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/user-associations')
      if (response.ok) {
        const result = await response.json()
        setAssociations(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching associations:', error)
    }
  }

  const fetchUsers = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/admin/users')
      if (response.ok) {
        const result = await response.json()
        setUsers(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching users:', error)
    }
  }

  const handleCreateAssociation = async () => {
    try {
      if (!form.accessor_user_id || !form.target_user_id) {
        toast({
          title: "Erro",
          description: "Selecione ambos os usuários para criar a associação.",
          variant: "destructive"
        })
        return
      }

      if (form.accessor_user_id === form.target_user_id) {
        toast({
          title: "Erro",
          description: "Um usuário não pode ser associado a si mesmo.",
          variant: "destructive"
        })
        return
      }

      const response = await makeAuthenticatedRequest('/api/user-associations', {
        method: 'POST',
        body: JSON.stringify(form)
      })

      if (response.ok) {
        toast({
          title: "Sucesso",
          description: "Associação criada com sucesso.",
        })
        setIsDialogOpen(false)
        resetForm()
        fetchAssociations()
      } else {
        const error = await response.json()
        throw new Error(error.message || 'Failed to create association')
      }
    } catch (error) {
      console.error('Error creating association:', error)
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao criar associação.",
        variant: "destructive"
      })
    }
  }

  const handleDeleteAssociation = async (associationId: string) => {
    try {
      const response = await makeAuthenticatedRequest(
        `/api/user-associations?id=${associationId}`,
        { method: 'DELETE' }
      )

      if (response.ok) {
        toast({
          title: "Sucesso",
          description: "Associação removida com sucesso.",
        })
        fetchAssociations()
      } else {
        throw new Error('Failed to delete association')
      }
    } catch (error) {
      console.error('Error deleting association:', error)
      toast({
        title: "Erro",
        description: "Erro ao remover associação.",
        variant: "destructive"
      })
    }
  }

  const resetForm = () => {
    setForm({
      accessor_user_id: '',
      target_user_id: '',
      association_type: 'secretary_to_doctor',
      permissions: {}
    })
  }

  const handlePermissionChange = (resource: string, action: string, checked: boolean) => {
    setForm(prev => {
      const newPermissions = { ...prev.permissions }
      
      if (!newPermissions[resource]) {
        newPermissions[resource] = []
      }
      
      if (checked) {
        if (!newPermissions[resource].includes(action)) {
          newPermissions[resource].push(action)
        }
      } else {
        newPermissions[resource] = newPermissions[resource].filter(a => a !== action)
        if (newPermissions[resource].length === 0) {
          delete newPermissions[resource]
        }
      }
      
      return { ...prev, permissions: newPermissions }
    })
  }

  const getAssociationTypeLabel = (type: string) => {
    switch (type) {
      case 'secretary_to_doctor':
        return 'Secretária → Médico'
      case 'doctor_to_doctor':
        return 'Médico → Médico'
      default:
        return type
    }
  }

  const getFilteredUsers = (role?: string) => {
    return users.filter(user => user.is_active && (!role || user.role === role))
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Carregando...</CardTitle>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Link className="h-5 w-5" />
              Associações de Usuários
            </CardTitle>
            <CardDescription>
              Gerencie o acesso entre usuários do sistema
            </CardDescription>
          </div>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                Nova Associação
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Criar Nova Associação</DialogTitle>
                <DialogDescription>
                  Associe usuários para permitir acesso compartilhado aos dados
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="association_type">Tipo de Associação</Label>
                    <Select 
                      value={form.association_type} 
                      onValueChange={(value: 'secretary_to_doctor' | 'doctor_to_doctor') => 
                        setForm({ ...form, association_type: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="secretary_to_doctor">Secretária → Médico</SelectItem>
                        <SelectItem value="doctor_to_doctor">Médico → Médico</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="accessor_user">
                      {form.association_type === 'secretary_to_doctor' ? 'Secretária' : 'Médico (Acessor)'}
                    </Label>
                    <Select 
                      value={form.accessor_user_id} 
                      onValueChange={(value) => setForm({ ...form, accessor_user_id: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o usuário" />
                      </SelectTrigger>
                      <SelectContent>
                        {getFilteredUsers(
                          form.association_type === 'secretary_to_doctor' ? 'secretary' : 'doctor'
                        ).map(user => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.name} ({user.email})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="target_user">Médico (Alvo)</Label>
                    <Select 
                      value={form.target_user_id} 
                      onValueChange={(value) => setForm({ ...form, target_user_id: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o médico" />
                      </SelectTrigger>
                      <SelectContent>
                        {getFilteredUsers('doctor').map(user => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.name} ({user.email})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label>Permissões Específicas (opcional)</Label>
                  <p className="text-sm text-muted-foreground mb-3">
                    Se não especificadas, serão usadas as permissões padrão do role
                  </p>
                  
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {Object.entries(AVAILABLE_PERMISSIONS).map(([resource, actions]) => (
                      <div key={resource} className="border rounded p-2">
                        <h4 className="font-medium mb-1 text-sm capitalize">{resource.replace('_', ' ')}</h4>
                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-1">
                          {actions.map(action => (
                            <div key={action} className="flex items-center space-x-1">
                              <Checkbox
                                id={`${resource}-${action}`}
                                checked={form.permissions[resource]?.includes(action) || false}
                                onCheckedChange={(checked) =>
                                  handlePermissionChange(resource, action, checked as boolean)
                                }
                              />
                              <Label
                                htmlFor={`${resource}-${action}`}
                                className="text-xs capitalize"
                              >
                                {action}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancelar
                </Button>
                <Button onClick={handleCreateAssociation}>
                  Criar Associação
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      
      <CardContent>
        {associations.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Users className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <p>Nenhuma associação configurada</p>
            <p className="text-sm mt-2">
              Crie associações para permitir que usuários acessem dados de outros usuários
            </p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Usuário Acessor</TableHead>
                <TableHead>Usuário Alvo</TableHead>
                <TableHead>Tipo</TableHead>
                <TableHead>Permissões</TableHead>
                <TableHead>Criado em</TableHead>
                <TableHead>Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {associations.map((association) => (
                <TableRow key={association.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{association.accessor_user.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {association.accessor_user.email}
                      </div>
                      <Badge variant="outline" className="text-xs mt-1">
                        {association.accessor_user.role}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{association.target_user.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {association.target_user.email}
                      </div>
                      <Badge variant="outline" className="text-xs mt-1">
                        {association.target_user.role}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {getAssociationTypeLabel(association.association_type)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {association.permissions ? (
                      <div className="space-y-1">
                        {Object.entries(association.permissions).map(([resource, actions]) => (
                          <div key={resource} className="text-xs">
                            <span className="font-medium">{resource}:</span> {actions.join(', ')}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <Badge variant="outline" className="text-xs">
                        Permissões padrão do role
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-sm">
                    {formatDateTimeBR(association.created_at)}
                  </TableCell>
                  <TableCell>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button size="sm" variant="destructive">
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Remover Associação</AlertDialogTitle>
                          <AlertDialogDescription>
                            Tem certeza que deseja remover a associação entre{' '}
                            <strong>{association.accessor_user.name}</strong> e{' '}
                            <strong>{association.target_user.name}</strong>?
                            Esta ação não pode ser desfeita.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancelar</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteAssociation(association.id)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            Remover
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}
