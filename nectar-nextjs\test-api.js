// Script para testar a API do sistema multi-usuário
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001';

// Dados de teste
const adminCredentials = {
  email: '<EMAIL>',
  password: 'sua_senha_aqui' // Substitua pela senha real
};

const testSecretaryData = {
  email: '<EMAIL>',
  password: 'senha123',
  name: '<PERSON><PERSON><PERSON> Teste',
  phone: '(11) 99999-9999',
  role: 'secretary'
};

const testDoctorData = {
  email: '<EMAIL>',
  password: 'senha123',
  name: 'Dr. <PERSON><PERSON>',
  phone: '(11) 88888-8888',
  role: 'doctor'
};

async function testUserCreation() {
  try {
    console.log('🧪 Testando criação de usuários...');
    
    // 1. Login como admin
    console.log('1. Fazendo login como admin...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(adminCredentials)
    });
    
    if (!loginResponse.ok) {
      throw new Error(`Login falhou: ${loginResponse.status}`);
    }
    
    const loginData = await loginResponse.json();
    const authToken = loginData.token; // Assumindo que o token é retornado
    
    console.log('✅ Login realizado com sucesso');
    
    // 2. Criar usuário secretária
    console.log('2. Criando usuário secretária...');
    const createSecretaryResponse = await fetch(`${BASE_URL}/api/admin/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(testSecretaryData)
    });
    
    if (!createSecretaryResponse.ok) {
      const errorData = await createSecretaryResponse.text();
      console.error('❌ Erro ao criar secretária:', errorData);
      return;
    }
    
    const secretaryData = await createSecretaryResponse.json();
    console.log('✅ Secretária criada:', secretaryData.data.email);
    
    // 3. Criar usuário médico
    console.log('3. Criando usuário médico...');
    const createDoctorResponse = await fetch(`${BASE_URL}/api/admin/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(testDoctorData)
    });
    
    if (!createDoctorResponse.ok) {
      const errorData = await createDoctorResponse.text();
      console.error('❌ Erro ao criar médico:', errorData);
      return;
    }
    
    const doctorData = await createDoctorResponse.json();
    console.log('✅ Médico criado:', doctorData.data.email);
    
    // 4. Criar associação secretária → médico
    console.log('4. Criando associação secretária → médico...');
    const associationData = {
      accessor_user_id: secretaryData.data.id,
      target_user_id: doctorData.data.id,
      association_type: 'secretary_to_doctor',
      permissions: {
        appointments: ['read', 'create', 'update'],
        patients: ['read', 'create', 'update'],
        medical_records: ['read']
      }
    };
    
    const createAssociationResponse = await fetch(`${BASE_URL}/api/user-associations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(associationData)
    });
    
    if (!createAssociationResponse.ok) {
      const errorData = await createAssociationResponse.text();
      console.error('❌ Erro ao criar associação:', errorData);
      return;
    }
    
    const associationResult = await createAssociationResponse.json();
    console.log('✅ Associação criada:', associationResult.data.association_type);
    
    // 5. Testar acesso da secretária
    console.log('5. Testando acesso da secretária...');
    
    // Login como secretária
    const secretaryLoginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testSecretaryData.email,
        password: testSecretaryData.password
      })
    });
    
    if (!secretaryLoginResponse.ok) {
      console.error('❌ Login da secretária falhou');
      return;
    }
    
    const secretaryLoginData = await secretaryLoginResponse.json();
    const secretaryToken = secretaryLoginData.token;
    
    // Testar acesso aos usuários acessíveis
    const accessibleUsersResponse = await fetch(`${BASE_URL}/api/user-associations`, {
      headers: {
        'Authorization': `Bearer ${secretaryToken}`
      }
    });
    
    if (accessibleUsersResponse.ok) {
      const accessibleUsers = await accessibleUsersResponse.json();
      console.log('✅ Secretária pode acessar usuários:', accessibleUsers.data.length);
    } else {
      console.error('❌ Secretária não conseguiu acessar usuários');
    }
    
    console.log('🎉 Teste completo realizado com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro durante o teste:', error.message);
  }
}

// Executar teste
testUserCreation();
