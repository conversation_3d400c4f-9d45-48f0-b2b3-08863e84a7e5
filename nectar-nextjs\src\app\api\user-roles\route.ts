import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type UserRole = Tables<'user_roles'>
type UserRoleInsert = TablesInsert<'user_roles'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Temporary mock data until user_roles table is created
      const mockRoles = [
        {
          id: '1',
          user_id: userId,
          role_name: 'admin',
          created_at: new Date().toISOString()
        }
      ]

      return createApiResponse(mockRoles)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()

      // Temporary mock response until user_roles table is created
      const mockRole = {
        id: Date.now().toString(),
        user_id: userId,
        role_name: body.role_name,
        created_at: new Date().toISOString()
      }

      return createApiResponse(mockRole, 'Role assigned successfully', 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
