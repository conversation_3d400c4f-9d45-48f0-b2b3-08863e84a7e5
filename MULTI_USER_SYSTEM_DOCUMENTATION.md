# 🏥 Sistema Multi-Usuário Nectar - Documentação Completa

## 📋 Visão Geral

O sistema multi-usuário do Nectar permite que diferentes tipos de usuários (médicos, secretárias, administradores) compartilhem acesso controlado aos dados, mantendo a segurança e privacidade através de um sistema hierárquico de permissões.

## 🏗️ Arquitetura do Sistema

### **Componentes Principais**

1. **Tabelas de Usuários**
   - `auth.users` - Autenticação Supabase
   - `public.users` - Dados públicos dos usuários
   - `user_roles` - Roles múltiplos por usuário
   - `healthcare_professionals` - Dados específicos de médicos
   - `secretaries` - Dados específicos de secretárias

2. **Sistema de Associações**
   - `user_associations` - Relacionamentos entre usuários
   - `permissions` - Permissões por role e recurso
   - `user_access_logs` - Auditoria de acessos

3. **Funções de Segurança**
   - `has_access_to_user()` - Verificação de acesso
   - `get_accessible_users()` - Lista usuários acessíveis
   - `log_user_access()` - Registro de auditoria

## 🔐 Sistema de Permissões

### **Hierarquia de Permissões**

1. **Permissões Específicas da Associação** (maior prioridade)
   - Definidas na coluna `permissions` da tabela `user_associations`
   - Formato: `{"appointments": ["read", "update"], "patients": ["read"]}`
   - Sobrescreve permissões padrão do role

2. **Permissões Padrão do Role** (menor prioridade)
   - Definidas na tabela `permissions`
   - Aplicadas quando não há permissões específicas na associação

### **Tipos de Roles**

- **admin**: Acesso total ao sistema
- **doctor**: Acesso a pacientes, consultas, prontuários próprios
- **secretary**: Acesso controlado baseado em associações

### **Tipos de Associações**

- **secretary_to_doctor**: Secretária acessa dados de médico específico
- **doctor_to_doctor**: Médico compartilha dados com outro médico

## 🛠️ APIs Implementadas

### **1. User Associations API**
```
GET    /api/user-associations          # Listar associações
POST   /api/user-associations          # Criar associação
PATCH  /api/user-associations?id=...   # Atualizar associação
DELETE /api/user-associations?id=...   # Remover associação
```

### **2. Accessible Users API**
```
GET  /api/accessible-users             # Listar usuários acessíveis
POST /api/accessible-users             # Verificar acesso específico
```

### **3. Admin Users API (Atualizada)**
```
POST /api/admin/users                  # Criar usuário com senha
```

## 🔧 Componentes Frontend

### **1. UserAssociationsManager**
- Interface para gerenciar associações entre usuários
- Criação de associações com permissões específicas
- Visualização e remoção de associações existentes

### **2. Hooks Customizados**
- `useAccessibleUsers()` - Gerencia usuários acessíveis
- `useAccessibleDoctors()` - Específico para médicos
- `useUserAccess()` - Verifica acesso a usuário específico

## 📊 Fluxos de Uso

### **Fluxo 1: Admin Cria Usuários**
1. Admin acessa `dashboard/admin/usuarios`
2. Clica em "Novo Usuário"
3. Preenche dados incluindo senha inicial
4. Sistema cria usuário em `auth.users` via Admin API
5. Triggers automaticamente:
   - Sincronizam para `public.users`
   - Criam entrada em `user_roles`
   - Populam tabela específica (`healthcare_professionals` ou `secretaries`)

### **Fluxo 2: Admin Cria Associações**
1. Admin acessa aba "Associações"
2. Seleciona tipo de associação
3. Escolhe usuário acessor e usuário alvo
4. Define permissões específicas (opcional)
5. Sistema valida roles e cria associação

### **Fluxo 3: Secretária Acessa Dados do Médico**
1. Secretária faz login
2. Sistema consulta `get_accessible_users()`
3. Interface mostra dados dos médicos associados
4. RLS policies verificam acesso via `has_access_to_user()`
5. Acesso é registrado em `user_access_logs`

## 🔒 Segurança Implementada

### **Row Level Security (RLS)**
Todas as tabelas principais têm policies atualizadas:
```sql
-- Exemplo para appointments
CREATE POLICY "Users can view accessible appointments" ON appointments
  FOR SELECT USING (has_access_to_user(user_id, 'appointments', 'read'));
```

### **Validações de Segurança**
- Prevenção de auto-associação
- Validação de tipos de associação por role
- Constraints de integridade referencial
- Auditoria completa de acessos

### **Princípios de Segurança**
- **Least Privilege**: Acesso mínimo necessário
- **Defense in Depth**: Múltiplas camadas de validação
- **Audit Trail**: Log completo de ações

## 🚀 Como Usar

### **1. Criar Usuário Secretária**
```typescript
// Via interface admin
const userData = {
  email: "<EMAIL>",
  name: "Maria Secretária",
  phone: "(11) 99999-9999",
  password: "SenhaTemporaria123!",
  role: "secretary"
}
```

### **2. Associar Secretária a Médico**
```typescript
// Via interface admin
const association = {
  accessor_user_id: "secretary-uuid",
  target_user_id: "doctor-uuid",
  association_type: "secretary_to_doctor",
  permissions: {
    "appointments": ["read", "create", "update"],
    "patients": ["read", "update"]
  }
}
```

### **3. Verificar Acesso Programaticamente**
```typescript
// No frontend
const { hasAccess } = useUserAccess(doctorId, 'patients', 'read')

// Via API
const response = await fetch('/api/accessible-users', {
  method: 'POST',
  body: JSON.stringify({
    target_user_id: doctorId,
    resource: 'patients',
    action: 'read'
  })
})
```

## 📈 Monitoramento e Auditoria

### **Logs de Acesso**
```sql
-- Visualizar acessos recentes
SELECT 
  al.*,
  u1.name as accessor_name,
  u2.name as target_name
FROM user_access_logs al
JOIN users u1 ON al.accessor_user_id = u1.id
JOIN users u2 ON al.target_user_id = u2.id
ORDER BY al.created_at DESC
LIMIT 50;
```

### **Relatórios de Associações**
```sql
-- Associações ativas por tipo
SELECT 
  association_type,
  COUNT(*) as total,
  COUNT(CASE WHEN permissions IS NOT NULL THEN 1 END) as with_custom_permissions
FROM user_associations 
WHERE is_active = true
GROUP BY association_type;
```

## 🔧 Manutenção

### **Backup de Configurações**
```sql
-- Backup das associações
COPY (
  SELECT * FROM user_associations WHERE is_active = true
) TO '/backup/user_associations.csv' WITH CSV HEADER;
```

### **Limpeza de Logs Antigos**
```sql
-- Remover logs mais antigos que 90 dias
DELETE FROM user_access_logs 
WHERE created_at < NOW() - INTERVAL '90 days';
```

## 🐛 Troubleshooting

### **Problema: Usuário não consegue fazer login**
- Verificar se existe em `auth.users`
- Confirmar sincronização com `public.users`
- Validar `email_confirmed_at`

### **Problema: Acesso negado inesperado**
- Verificar associações ativas
- Confirmar permissões específicas vs. padrão do role
- Consultar logs de acesso para detalhes

### **Problema: Performance lenta**
- Verificar índices nas tabelas de associação
- Otimizar função `has_access_to_user()`
- Considerar cache de permissões

## 📚 Referências

- [Supabase RLS Documentation](https://supabase.com/docs/guides/auth/row-level-security)
- [PostgreSQL JSONB](https://www.postgresql.org/docs/current/datatype-json.html)
- [Next.js API Routes](https://nextjs.org/docs/api-routes/introduction)

---

**Sistema implementado com sucesso em: 2025-01-12**
**Versão: 1.0**
**Status: Produção Ready** ✅
