// Script para testar a API como secretária
const BASE_URL = 'http://localhost:3001';

async function testSecretaryAPI() {
  console.log('🧪 Testando API como secretária...\n');
  
  try {
    // 1. Login como secretária
    console.log('1. Fazendo login como secretária...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'senha_da_secretaria' // Substitua pela senha correta
      })
    });
    
    if (!loginResponse.ok) {
      console.error('❌ Login falhou:', loginResponse.status);
      return;
    }
    
    const loginData = await loginResponse.json();
    console.log('✅ Login realizado com sucesso');
    
    // 2. Testar API de healthcare professionals
    console.log('2. Testando API de healthcare professionals...');
    const professionalsResponse = await fetch(`${BASE_URL}/api/healthcare-professionals`, {
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Cookie': loginResponse.headers.get('set-cookie') || ''
      }
    });
    
    if (!professionalsResponse.ok) {
      console.error('❌ API de professionals falhou:', professionalsResponse.status);
      const errorText = await professionalsResponse.text();
      console.error('Erro:', errorText);
      return;
    }
    
    const professionalsData = await professionalsResponse.json();
    console.log('✅ API de professionals funcionou');
    console.log('📋 Profissionais retornados:', professionalsData.data?.length || 0);
    
    if (professionalsData.data && professionalsData.data.length > 0) {
      console.log('👨‍⚕️ Profissionais encontrados:');
      professionalsData.data.forEach(prof => {
        console.log(`  - ${prof.name} (${prof.specialty})`);
      });
    } else {
      console.log('⚠️ Nenhum profissional encontrado');
    }
    
    // 3. Testar API de user associations
    console.log('3. Testando API de user associations...');
    const associationsResponse = await fetch(`${BASE_URL}/api/user-associations`, {
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Cookie': loginResponse.headers.get('set-cookie') || ''
      }
    });
    
    if (!associationsResponse.ok) {
      console.error('❌ API de associations falhou:', associationsResponse.status);
      return;
    }
    
    const associationsData = await associationsResponse.json();
    console.log('✅ API de associations funcionou');
    console.log('🔗 Associações retornadas:', associationsData.data?.length || 0);
    
    console.log('\n🎉 Teste completo!');
    
  } catch (error) {
    console.error('❌ Erro durante o teste:', error.message);
  }
}

// Executar teste
testSecretaryAPI();
