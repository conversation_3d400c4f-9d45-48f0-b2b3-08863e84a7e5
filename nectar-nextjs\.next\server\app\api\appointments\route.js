/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/appointments/route";
exports.ids = ["app/api/appointments/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fappointments%2Froute&page=%2Fapi%2Fappointments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fappointments%2Froute&page=%2Fapi%2Fappointments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_cirov_Documents_next_js_nectar_nectar_nextjs_src_app_api_appointments_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/appointments/route.ts */ \"(rsc)/./src/app/api/appointments/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/appointments/route\",\n        pathname: \"/api/appointments\",\n        filename: \"route\",\n        bundlePath: \"app/api/appointments/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\api\\\\appointments\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_cirov_Documents_next_js_nectar_nectar_nextjs_src_app_api_appointments_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fappointments%2Froute&page=%2Fapi%2Fappointments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/appointments/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/appointments/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./src/lib/api-utils.ts\");\n\nasync function GET(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withAuth)(request, async (userId, supabase)=>{\n        try {\n            const { searchParams } = new URL(request.url);\n            const date = searchParams.get('date');\n            const patientId = searchParams.get('patient_id');\n            // Get accessible users first\n            const { data: accessibleUsers, error: accessError } = await supabase.rpc('get_accessible_users', {\n                current_user_id: userId\n            });\n            if (accessError) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(accessError);\n            }\n            const accessibleUserIds = accessibleUsers?.map((user)=>user.user_id) || [];\n            if (accessibleUserIds.length === 0) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)([]);\n            }\n            // Get healthcare professional ID for current user if they are a doctor\n            const { data: currentUserProfessional, error: profError } = await supabase.from('healthcare_professionals').select('id').eq('user_id', userId).single();\n            console.log('🏥 Current user professional:', {\n                currentUserProfessional,\n                profError,\n                userId\n            });\n            let query = supabase.from('appointments').select(`\n          *,\n          patients!inner(name),\n          healthcare_professionals(name, specialty)\n        `);\n            // Filter appointments by:\n            // 1. user_id in accessible users (appointments created by/for accessible users)\n            // 2. OR healthcare_professional_id matches current user (appointments assigned to this doctor)\n            if (currentUserProfessional) {\n                const orFilter = `user_id.in.(${accessibleUserIds.join(',')}),healthcare_professional_id.eq.${currentUserProfessional.id}`;\n                console.log('📋 Using OR filter:', orFilter);\n                query = query.or(orFilter);\n            } else {\n                console.log('📋 Using simple user_id filter:', accessibleUserIds);\n                query = query.in('user_id', accessibleUserIds);\n            }\n            if (date) {\n                // Parse date in local timezone to avoid UTC conversion issues\n                const [year, month, day] = date.split('-').map(Number);\n                const startDate = new Date(year, month - 1, day, 0, 0, 0, 0);\n                const endDate = new Date(year, month - 1, day + 1, 0, 0, 0, 0);\n                // Convert to ISO string but maintain local timezone context\n                // This ensures we filter correctly for Brazilian timezone\n                query = query.gte('start_time', startDate.toISOString()).lt('start_time', endDate.toISOString());\n            }\n            if (patientId) {\n                query = query.eq('patient_id', patientId);\n            }\n            // Order by start_time - ascending for date-specific queries (agenda), descending for patient history\n            const orderDirection = date ? {\n                ascending: true\n            } : {\n                ascending: false\n            };\n            const { data: appointments, error } = await query.order('start_time', orderDirection);\n            if (error) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n            }\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(appointments);\n        } catch (error) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n        }\n    });\n}\nasync function POST(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withAuth)(request, async (userId, supabase)=>{\n        try {\n            const body = await request.json();\n            const { procedures = [], ...appointmentBody } = body;\n            // Get accessible users to validate target user\n            const { data: accessibleUsers, error: accessError } = await supabase.rpc('get_accessible_users', {\n                current_user_id: userId\n            });\n            if (accessError) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(accessError);\n            }\n            const accessibleUserIds = accessibleUsers?.map((user)=>user.user_id) || [];\n            // Determine the target user_id for the appointment\n            let targetUserId = userId // Default to current user\n            ;\n            // If a healthcare professional is specified, find their user_id\n            if (body.healthcare_professional_id) {\n                const { data: professional, error: profError } = await supabase.from('healthcare_professionals').select('user_id').eq('id', body.healthcare_professional_id).single();\n                if (!profError && professional && accessibleUserIds.includes(professional.user_id)) {\n                    targetUserId = professional.user_id;\n                    console.log('🎯 Setting appointment user_id to professional:', professional.user_id);\n                }\n            }\n            // Legacy support: If a specific doctor_id is specified and user has access to them, use that\n            if (body.doctor_id && accessibleUserIds.includes(body.doctor_id)) {\n                targetUserId = body.doctor_id;\n                console.log('🎯 Setting appointment user_id to doctor_id:', body.doctor_id);\n            }\n            console.log('📝 Creating appointment with user_id:', targetUserId, 'for professional:', body.healthcare_professional_id);\n            const appointmentData = {\n                ...appointmentBody,\n                user_id: targetUserId,\n                status: body.status || 'scheduled',\n                start_time: new Date(body.start_time).toISOString(),\n                end_time: new Date(body.end_time).toISOString(),\n                healthcare_professional_id: body.healthcare_professional_id || null,\n                total_price: body.total_price || null,\n                recurrence_rule: body.has_recurrence ? JSON.stringify({\n                    type: body.recurrence_type,\n                    interval: body.recurrence_interval,\n                    days: body.recurrence_days,\n                    end_type: body.recurrence_end_type,\n                    end_date: body.recurrence_end_date,\n                    count: body.recurrence_count\n                }) : null,\n                recurrence_end_date: body.recurrence_end_date || null\n            };\n            // Remove doctor_id from appointment data as it's not a column in appointments table\n            delete appointmentData.doctor_id;\n            // Create appointment\n            const { data: appointment, error: appointmentError } = await supabase.from('appointments').insert(appointmentData).select(`\n          *,\n          patients!inner(name),\n          healthcare_professionals(name, specialty)\n        `).single();\n            if (appointmentError) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(appointmentError);\n            }\n            // Create appointment procedures if any\n            if (procedures.length > 0) {\n                const procedureInserts = procedures.map((proc)=>({\n                        appointment_id: appointment.id,\n                        procedure_id: proc.procedure_id,\n                        quantity: parseInt(proc.quantity),\n                        unit_price: parseFloat(proc.unit_price),\n                        total_price: parseFloat(proc.total_price)\n                    }));\n                const { error: proceduresError } = await supabase.from('appointment_procedures').insert(procedureInserts);\n                if (proceduresError) {\n                    // Rollback appointment if procedures fail\n                    await supabase.from('appointments').delete().eq('id', appointment.id);\n                    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(proceduresError);\n                }\n            }\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(appointment, undefined, 201);\n        } catch (error) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/appointments/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api-utils.ts":
/*!******************************!*\
  !*** ./src/lib/api-utils.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApiResponse: () => (/* binding */ createApiResponse),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   withAuth: () => (/* binding */ withAuth),\n/* harmony export */   withAuthAndPermission: () => (/* binding */ withAuthAndPermission)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_permissions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/permissions */ \"(rsc)/./src/lib/permissions.ts\");\n\n\n\nfunction createApiResponse(data, message, status = 200) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        data,\n        message\n    }, {\n        status\n    });\n}\nasync function withAuth(request, handler) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        console.log('🔐 Auth check:', {\n            hasUser: !!user,\n            userId: user?.id,\n            email: user?.email,\n            error: authError?.message\n        });\n        if (authError || !user) {\n            console.error('❌ Auth failed:', authError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        return await handler(user.id, supabase);\n    } catch (error) {\n        console.error('API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function withAuthAndPermission(request, resource, action, handler) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return createApiResponse(undefined, 'Unauthorized', 401);\n        }\n        // Check permissions\n        const hasAccess = await (0,_lib_permissions__WEBPACK_IMPORTED_MODULE_2__.hasPermission)(user.id, resource, action);\n        if (!hasAccess) {\n            return createApiResponse(undefined, 'Forbidden: Insufficient permissions', 403);\n        }\n        return await handler(user.id, supabase);\n    } catch (error) {\n        console.error('API Error:', error);\n        return createApiResponse(undefined, error instanceof Error ? error.message : 'Internal server error', 500);\n    }\n}\nfunction handleApiError(error) {\n    console.error('API Error:', error);\n    if (error?.code === 'PGRST116') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Resource not found'\n        }, {\n            status: 404\n        });\n    }\n    if (error?.code === '23505') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Resource already exists'\n        }, {\n            status: 409\n        });\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: error?.message || 'Internal server error'\n    }, {\n        status: 500\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/permissions.ts":
/*!********************************!*\
  !*** ./src/lib/permissions.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_PERMISSIONS: () => (/* binding */ DEFAULT_PERMISSIONS),\n/* harmony export */   getUserPermissions: () => (/* binding */ getUserPermissions),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hasRole: () => (/* binding */ hasRole),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   requirePermission: () => (/* binding */ requirePermission),\n/* harmony export */   requireRole: () => (/* binding */ requireRole)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://zmwdnemlzndjavlriyrc.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Create a Supabase client with service role key for server-side operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n/**\n * Check if a user has a specific permission\n */ async function hasPermission(userId, resource, action) {\n    try {\n        // Get user roles\n        const { data: userRoles, error: rolesError } = await supabaseAdmin.from('user_roles').select('role_name').eq('user_id', userId);\n        if (rolesError || !userRoles || userRoles.length === 0) {\n            return false;\n        }\n        // Check if any of the user's roles have the required permission\n        const roleNames = userRoles.map((role)=>role.role_name);\n        const { data: permissions, error: permissionsError } = await supabaseAdmin.from('permissions').select('*').in('role_name', roleNames).eq('resource', resource).eq('action', action);\n        if (permissionsError) {\n            console.error('Error checking permissions:', permissionsError);\n            return false;\n        }\n        return permissions && permissions.length > 0;\n    } catch (error) {\n        console.error('Error in hasPermission:', error);\n        return false;\n    }\n}\n/**\n * Check if a user has any of the specified roles\n */ async function hasRole(userId, roles) {\n    try {\n        const { data: userRoles, error } = await supabaseAdmin.from('user_roles').select('role_name').eq('user_id', userId).in('role_name', roles);\n        if (error) {\n            console.error('Error checking roles:', error);\n            return false;\n        }\n        return userRoles && userRoles.length > 0;\n    } catch (error) {\n        console.error('Error in hasRole:', error);\n        return false;\n    }\n}\n/**\n * Get all permissions for a user\n */ async function getUserPermissions(userId) {\n    try {\n        // Get user roles\n        const { data: userRoles, error: rolesError } = await supabaseAdmin.from('user_roles').select('role_name').eq('user_id', userId);\n        if (rolesError || !userRoles || userRoles.length === 0) {\n            return [];\n        }\n        // Get all permissions for these roles\n        const roleNames = userRoles.map((role)=>role.role_name);\n        const { data: permissions, error: permissionsError } = await supabaseAdmin.from('permissions').select('resource, action').in('role_name', roleNames);\n        if (permissionsError) {\n            console.error('Error getting user permissions:', permissionsError);\n            return [];\n        }\n        return permissions || [];\n    } catch (error) {\n        console.error('Error in getUserPermissions:', error);\n        return [];\n    }\n}\n/**\n * Check if a user is an admin\n */ async function isAdmin(userId) {\n    return hasRole(userId, [\n        'admin'\n    ]);\n}\n/**\n * Middleware function to check permissions for API routes\n */ function requirePermission(resource, action) {\n    return async (userId)=>{\n        return hasPermission(userId, resource, action);\n    };\n}\n/**\n * Middleware function to check roles for API routes\n */ function requireRole(roles) {\n    return async (userId)=>{\n        return hasRole(userId, roles);\n    };\n}\n/**\n * Default permissions for each role\n */ const DEFAULT_PERMISSIONS = {\n    admin: [\n        // Full access to everything\n        {\n            resource: 'appointments',\n            action: 'create'\n        },\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'appointments',\n            action: 'update'\n        },\n        {\n            resource: 'appointments',\n            action: 'delete'\n        },\n        {\n            resource: 'patients',\n            action: 'create'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'update'\n        },\n        {\n            resource: 'patients',\n            action: 'delete'\n        },\n        {\n            resource: 'medical_records',\n            action: 'create'\n        },\n        {\n            resource: 'medical_records',\n            action: 'read'\n        },\n        {\n            resource: 'medical_records',\n            action: 'update'\n        },\n        {\n            resource: 'medical_records',\n            action: 'delete'\n        },\n        {\n            resource: 'procedures',\n            action: 'create'\n        },\n        {\n            resource: 'procedures',\n            action: 'read'\n        },\n        {\n            resource: 'procedures',\n            action: 'update'\n        },\n        {\n            resource: 'procedures',\n            action: 'delete'\n        },\n        {\n            resource: 'settings',\n            action: 'read'\n        },\n        {\n            resource: 'settings',\n            action: 'update'\n        }\n    ],\n    doctor: [\n        // Can manage appointments and patients, read procedures\n        {\n            resource: 'appointments',\n            action: 'create'\n        },\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'appointments',\n            action: 'update'\n        },\n        {\n            resource: 'patients',\n            action: 'create'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'update'\n        },\n        {\n            resource: 'medical_records',\n            action: 'create'\n        },\n        {\n            resource: 'medical_records',\n            action: 'read'\n        },\n        {\n            resource: 'medical_records',\n            action: 'update'\n        },\n        {\n            resource: 'procedures',\n            action: 'read'\n        }\n    ],\n    secretary: [\n        // Can manage appointments and patients, read procedures\n        {\n            resource: 'appointments',\n            action: 'create'\n        },\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'appointments',\n            action: 'update'\n        },\n        {\n            resource: 'patients',\n            action: 'create'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'update'\n        },\n        {\n            resource: 'procedures',\n            action: 'read'\n        }\n    ],\n    assistant: [\n        // Read-only access to appointments and patients\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/permissions.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://zmwdnemlzndjavlriyrc.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inptd2RuZW1sem5kamF2bHJpeXJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MTE5NTksImV4cCI6MjA2NzM4Nzk1OX0.XNRQjZmMZ7s4aKrJVSQFlu9ASryGJc5fBX6iNnjOPEM\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fappointments%2Froute&page=%2Fapi%2Fappointments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fappointments%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();